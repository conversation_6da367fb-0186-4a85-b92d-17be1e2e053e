// Script para corrigir preços dos módulos básicos
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function fixModulePrices() {
  try {
    console.log('🔧 Iniciando correção dos preços dos módulos...');

    // 1. Corrigir preços dos módulos básicos (devem ser R$ 0,00)
    console.log('📦 Corrigindo preços dos módulos básicos...');
    
    const basicModules = ['BASIC', 'ADMIN', 'SCHEDULING'];
    
    for (const moduleType of basicModules) {
      const result = await prisma.subscriptionModule.updateMany({
        where: {
          moduleType: moduleType,
          active: true
        },
        data: {
          pricePerMonth: 0
        }
      });
      
      console.log(`✅ ${result.count} módulos ${moduleType} corrigidos para R$ 0,00`);
    }

    // 2. Verificar módulos que ainda têm preços incorretos
    console.log('\n🔍 Verificando módulos com preços...');
    
    const modulesWithPrices = await prisma.subscriptionModule.findMany({
      where: {
        pricePerMonth: {
          gt: 0
        },
        active: true
      },
      include: {
        subscription: {
          include: {
            company: {
              select: { name: true }
            }
          }
        }
      }
    });

    if (modulesWithPrices.length > 0) {
      console.log(`⚠️  Encontrados ${modulesWithPrices.length} módulos com preços > R$ 0,00:`);
      
      for (const module of modulesWithPrices) {
        console.log(`  - ${module.moduleType}: R$ ${module.pricePerMonth} (${module.subscription.company.name})`);
        
        // Se for módulo básico, corrigir para R$ 0,00
        if (basicModules.includes(module.moduleType)) {
          await prisma.subscriptionModule.update({
            where: { id: module.id },
            data: { pricePerMonth: 0 }
          });
          console.log(`    ✅ Corrigido para R$ 0,00`);
        }
      }
    } else {
      console.log('✅ Todos os módulos estão com preços corretos');
    }

    // 3. Verificar e mostrar resumo por empresa
    console.log('\n📊 Resumo por empresa:');
    
    const companies = await prisma.company.findMany({
      include: {
        subscription: {
          include: {
            modules: {
              where: { active: true }
            }
          }
        }
      },
      where: {
        subscription: {
          isNot: null
        }
      }
    });

    for (const company of companies) {
      if (company.subscription) {
        console.log(`\n🏢 ${company.name}:`);
        console.log(`   Preço total: R$ ${company.subscription.pricePerMonth}`);
        console.log(`   Usuários: ${company.subscription.userLimit || 'N/A'}`);
        console.log(`   Módulos ativos:`);
        
        for (const module of company.subscription.modules) {
          const price = parseFloat(module.pricePerMonth);
          const status = price === 0 ? '✅' : '⚠️';
          console.log(`     ${status} ${module.moduleType}: R$ ${price.toFixed(2)}`);
        }
      }
    }

    console.log('\n🎉 Correção concluída!');
    
  } catch (error) {
    console.error('❌ Erro durante a correção:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  fixModulePrices();
}

module.exports = { fixModulePrices };
