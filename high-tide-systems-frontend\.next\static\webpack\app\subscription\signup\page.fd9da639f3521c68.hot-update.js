"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/subscription/signup/page",{

/***/ "(app-pages-browser)/./src/app/subscription/signup/page.js":
/*!*********************************************!*\
  !*** ./src/app/subscription/signup/page.js ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SubscriptionSignupPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_input_mask__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @react-input/mask */ \"(app-pages-browser)/./node_modules/@react-input/mask/module/InputMask.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _services_subscriptionService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/subscriptionService */ \"(app-pages-browser)/./src/services/subscriptionService.js\");\n/* harmony import */ var _utils_pricing__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/pricing */ \"(app-pages-browser)/./src/utils/pricing.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SubscriptionSignupPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [documentType, setDocumentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"cpf\");\n    const [billingCycle, setBillingCycle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"monthly\");\n    const [userCount, setUserCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5); // Número de usuários selecionado\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Dados pessoais\n        login: \"\",\n        fullName: \"\",\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        cpf: \"\",\n        cnpj: \"\",\n        birthDate: \"\",\n        address: \"\",\n        phone: \"\",\n        // Dados da empresa\n        companyName: \"\",\n        companyTradingName: \"\",\n        companyCnpj: \"\",\n        companyPhone: \"\",\n        companyAddress: \"\",\n        companyCity: \"\",\n        companyState: \"\",\n        companyPostalCode: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Detecta se está em ambiente de desenvolvimento (localhost)\n    const isDevelopment =  true && (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');\n    // Dados de teste para desenvolvimento\n    const devData = {\n        login: \"teste_dev\",\n        fullName: \"Usuário de Teste\",\n        email: \"<EMAIL>\",\n        password: \"123456\",\n        confirmPassword: \"123456\",\n        cpf: \"123.456.789-00\",\n        cnpj: \"12.345.678/0001-90\",\n        birthDate: \"1990-01-01\",\n        address: \"Rua de Teste, 123\",\n        phone: \"(11) 99999-9999\",\n        companyName: \"Empresa de Teste Ltda\",\n        companyTradingName: \"Teste Corp\",\n        companyCnpj: \"98.765.432/0001-10\",\n        companyPhone: \"(11) 88888-8888\",\n        companyAddress: \"Av. Empresarial, 456\",\n        companyCity: \"São Paulo\",\n        companyState: \"SP\",\n        companyPostalCode: \"01234-567\"\n    };\n    // Usar as funções centralizadas de pricing\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Limpa o erro do campo quando o usuário começa a digitar\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: undefined\n                }));\n        }\n    };\n    const removeMask = (value)=>{\n        return value ? value.replace(/\\D/g, \"\") : \"\";\n    };\n    // Função para preencher dados de desenvolvimento\n    const fillDevData = ()=>{\n        if (isDevelopment) {\n            setFormData(devData);\n        }\n    };\n    // Função para pular para o próximo passo (apenas em desenvolvimento)\n    const skipToStep = (step)=>{\n        if (isDevelopment) {\n            if (step >= 2 && currentStep === 1) {\n                fillDevData();\n            }\n            setCurrentStep(step);\n        }\n    };\n    const validateStep1 = ()=>{\n        const newErrors = {};\n        if (!formData.login) newErrors.login = \"Login é obrigatório\";\n        if (!formData.fullName) newErrors.fullName = \"Nome é obrigatório\";\n        if (!formData.email || !/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = \"Email inválido\";\n        }\n        if (!formData.password || formData.password.length < 6) {\n            newErrors.password = \"Senha deve ter no mínimo 6 caracteres\";\n        }\n        if (formData.password !== formData.confirmPassword) {\n            newErrors.confirmPassword = \"Senhas não conferem\";\n        }\n        if (documentType === \"cpf\" && !formData.cpf) {\n            newErrors.cpf = \"CPF é obrigatório\";\n        }\n        if (documentType === \"cnpj\" && !formData.cnpj) {\n            newErrors.cnpj = \"CNPJ é obrigatório\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const validateStep2 = ()=>{\n        const newErrors = {};\n        if (!formData.companyName) newErrors.companyName = \"Nome da empresa é obrigatório\";\n        if (!formData.companyCnpj) newErrors.companyCnpj = \"CNPJ da empresa é obrigatório\";\n        if (!formData.companyPhone) newErrors.companyPhone = \"Telefone da empresa é obrigatório\";\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleNextStep = ()=>{\n        if (currentStep === 1 && validateStep1()) {\n            setCurrentStep(2);\n        } else if (currentStep === 2 && validateStep2()) {\n            setCurrentStep(3);\n        }\n    };\n    const handlePrevStep = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const handleSubmit = async ()=>{\n        if (!validateStep2()) return;\n        setIsLoading(true);\n        try {\n            // 1. Registra o usuário e empresa em uma única operação\n            const userResponse = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.post(\"/auth/register\", {\n                // Dados do usuário\n                login: formData.login,\n                fullName: formData.fullName,\n                email: formData.email,\n                password: formData.password,\n                cpf: documentType === \"cpf\" ? removeMask(formData.cpf) : undefined,\n                cnpj: documentType === \"cnpj\" ? removeMask(formData.cnpj) : undefined,\n                birthDate: formData.birthDate || undefined,\n                address: formData.address || undefined,\n                phone: formData.phone ? removeMask(formData.phone) : undefined,\n                // Dados da empresa\n                companyName: formData.companyName,\n                companyTradingName: formData.companyTradingName,\n                companyCnpj: formData.companyCnpj,\n                companyPhone: formData.companyPhone,\n                companyAddress: formData.companyAddress,\n                companyCity: formData.companyCity,\n                companyState: formData.companyState,\n                companyPostalCode: formData.companyPostalCode\n            });\n            // 2. Salva o token de autenticação\n            if (userResponse.data.token) {\n                localStorage.setItem('token', userResponse.data.token);\n            }\n            // 3. Cria a sessão de checkout do Stripe\n            const pricing = (0,_utils_pricing__WEBPACK_IMPORTED_MODULE_7__.calculatePrice)(userCount, billingCycle === 'yearly');\n            const checkoutResponse = await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_6__.subscriptionService.createCheckoutSession({\n                billingCycle,\n                userLimit: userCount,\n                price: pricing.finalPrice,\n                discount: pricing.discount\n            });\n            // 4. Redireciona para o Stripe Checkout\n            if (checkoutResponse.url) {\n                window.location.href = checkoutResponse.url;\n            }\n        } catch (error) {\n            setErrors((prev)=>{\n                var _error_response_data, _error_response;\n                return {\n                    ...prev,\n                    submit: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Erro ao processar cadastro\"\n                };\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const inputClasses = \"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 dark:text-white bg-white dark:bg-gray-700\";\n    const labelClasses = \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\";\n    const iconContainerClasses = \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-xl w-full max-w-4xl p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-12 w-12 text-orange-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"mx-4 text-3xl font-bold text-gray-900 dark:text-white\",\n                                    children: \"Criar conta e assinar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center space-x-4 mb-6\",\n                            children: [\n                                1,\n                                2,\n                                3\n                            ].map((step)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium \".concat(currentStep >= step ? 'bg-orange-500 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300'),\n                                            children: currentStep > step ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 259,\n                                                columnNumber: 41\n                                            }, this) : step\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this),\n                                        step < 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-1 mx-2 \".concat(currentStep > step ? 'bg-orange-500' : 'bg-gray-200 dark:bg-gray-600')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 262,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, step, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                            children: [\n                                currentStep === 1 && \"Dados pessoais\",\n                                currentStep === 2 && \"Dados da empresa\",\n                                currentStep === 3 && \"Plano e pagamento\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this),\n                        isDevelopment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-yellow-800 dark:text-yellow-300 mb-2 text-center\",\n                                    children: \"\\uD83D\\uDEA7 Modo Desenvolvimento - Atalhos para testes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: fillDevData,\n                                            className: \"px-3 py-1 bg-yellow-500 text-white rounded text-xs hover:bg-yellow-600\",\n                                            children: \"Preencher Dados\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>skipToStep(1),\n                                            className: \"px-3 py-1 rounded text-xs \".concat(currentStep === 1 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                            children: \"Passo 1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>skipToStep(2),\n                                            className: \"px-3 py-1 rounded text-xs \".concat(currentStep === 2 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                            children: \"Passo 2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>skipToStep(3),\n                                            className: \"px-3 py-1 rounded text-xs \".concat(currentStep === 3 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                            children: \"Passo 3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, this),\n                errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg mb-6\",\n                    children: errors.submit\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                    lineNumber: 317,\n                    columnNumber: 11\n                }, this),\n                currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"login\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"login\",\n                                                    name: \"login\",\n                                                    type: \"text\",\n                                                    value: formData.login,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.login && \"border-red-500\"),\n                                                    placeholder: \"Seu login\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.login && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.login\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 345,\n                                            columnNumber: 34\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"fullName\",\n                                            children: \"Nome Completo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"fullName\",\n                                                    name: \"fullName\",\n                                                    type: \"text\",\n                                                    value: formData.fullName,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.fullName && \"border-red-500\"),\n                                                    placeholder: \"Seu nome completo\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.fullName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.fullName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 367,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"email\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 374,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"email\",\n                                                    name: \"email\",\n                                                    type: \"email\",\n                                                    value: formData.email,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.email && \"border-red-500\"),\n                                                    placeholder: \"<EMAIL>\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 391,\n                                            columnNumber: 34\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 373,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"password\",\n                                            children: \"Senha\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 395,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: \"password\",\n                                                    value: formData.password,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.password && \"border-red-500\"),\n                                                    placeholder: \"••••••••\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 396,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.password\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 412,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 394,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 372,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"confirmPassword\",\n                                            children: \"Confirmar Senha\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 419,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"confirmPassword\",\n                                                    name: \"confirmPassword\",\n                                                    type: \"password\",\n                                                    value: formData.confirmPassword,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.confirmPassword && \"border-red-500\"),\n                                                    placeholder: \"••••••••\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 420,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.confirmPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.confirmPassword\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 436,\n                                            columnNumber: 44\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 418,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            children: \"Tipo de Documento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 440,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"documentType\",\n                                                            value: \"cpf\",\n                                                            checked: documentType === \"cpf\",\n                                                            onChange: (e)=>setDocumentType(e.target.value),\n                                                            className: \"mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"CPF\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"documentType\",\n                                                            value: \"cnpj\",\n                                                            checked: documentType === \"cnpj\",\n                                                            onChange: (e)=>setDocumentType(e.target.value),\n                                                            className: \"mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"CNPJ\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 441,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 439,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 417,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: documentType,\n                                            children: documentType === \"cpf\" ? \"CPF\" : \"CNPJ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 471,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_input_mask__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    mask: documentType === \"cpf\" ? \"___.___.___-__\" : \"__.___.___/____-__\",\n                                                    replacement: {\n                                                        _: /\\d/\n                                                    },\n                                                    name: documentType,\n                                                    value: formData[documentType],\n                                                    onChange: handleChange,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors[documentType] && \"border-red-500\"),\n                                                    placeholder: documentType === \"cpf\" ? \"000.000.000-00\" : \"00.000.000/0000-00\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 474,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors[documentType] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors[documentType]\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 489,\n                                            columnNumber: 42\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 470,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"phone\",\n                                            children: \"Telefone (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 493,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_input_mask__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    mask: \"(__) _____-____\",\n                                                    replacement: {\n                                                        _: /\\d/\n                                                    },\n                                                    name: \"phone\",\n                                                    value: formData.phone,\n                                                    onChange: handleChange,\n                                                    className: inputClasses,\n                                                    placeholder: \"(11) 99999-9999\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 494,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 492,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 469,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"birthDate\",\n                                            children: \"Data de Nascimento (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 515,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"birthDate\",\n                                                    name: \"birthDate\",\n                                                    type: \"date\",\n                                                    value: formData.birthDate,\n                                                    onChange: handleChange,\n                                                    className: inputClasses,\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 516,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 514,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"address\",\n                                            children: \"Endere\\xe7o (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 533,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"address\",\n                                                    name: \"address\",\n                                                    type: \"text\",\n                                                    value: formData.address,\n                                                    onChange: handleChange,\n                                                    className: inputClasses,\n                                                    placeholder: \"Seu endere\\xe7o\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 534,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 532,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 513,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between pt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/landing\",\n                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 557,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Voltar\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 553,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleNextStep,\n                                    className: \"inline-flex items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600\",\n                                    children: [\n                                        \"Pr\\xf3ximo\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-4 h-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 567,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 561,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 552,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                    lineNumber: 324,\n                    columnNumber: 11\n                }, this),\n                currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyName\",\n                                            children: \"Nome da Empresa\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 579,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"companyName\",\n                                                    name: \"companyName\",\n                                                    type: \"text\",\n                                                    value: formData.companyName,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.companyName && \"border-red-500\"),\n                                                    placeholder: \"Raz\\xe3o social da empresa\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.companyName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.companyName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 596,\n                                            columnNumber: 40\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 578,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyTradingName\",\n                                            children: \"Nome Fantasia (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 600,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 603,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"companyTradingName\",\n                                                    name: \"companyTradingName\",\n                                                    type: \"text\",\n                                                    value: formData.companyTradingName,\n                                                    onChange: handleChange,\n                                                    className: inputClasses,\n                                                    placeholder: \"Nome fantasia\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 601,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 599,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 577,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyCnpj\",\n                                            children: \"CNPJ da Empresa\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 622,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 624,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_input_mask__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    mask: \"__.___.___/____-__\",\n                                                    replacement: {\n                                                        _: /\\d/\n                                                    },\n                                                    name: \"companyCnpj\",\n                                                    value: formData.companyCnpj,\n                                                    onChange: handleChange,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.companyCnpj && \"border-red-500\"),\n                                                    placeholder: \"00.000.000/0000-00\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 623,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.companyCnpj && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.companyCnpj\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 638,\n                                            columnNumber: 40\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 621,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyPhone\",\n                                            children: \"Telefone da Empresa\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 642,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_input_mask__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    mask: \"(__) _____-____\",\n                                                    replacement: {\n                                                        _: /\\d/\n                                                    },\n                                                    name: \"companyPhone\",\n                                                    value: formData.companyPhone,\n                                                    onChange: handleChange,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.companyPhone && \"border-red-500\"),\n                                                    placeholder: \"(11) 99999-9999\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 643,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.companyPhone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.companyPhone\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 658,\n                                            columnNumber: 41\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 641,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 620,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: labelClasses,\n                                    htmlFor: \"companyAddress\",\n                                    children: \"Endere\\xe7o da Empresa (opcional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 664,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: iconContainerClasses,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 667,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 666,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"companyAddress\",\n                                            name: \"companyAddress\",\n                                            type: \"text\",\n                                            value: formData.companyAddress,\n                                            onChange: handleChange,\n                                            className: inputClasses,\n                                            placeholder: \"Endere\\xe7o completo da empresa\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 669,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 665,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 663,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyCity\",\n                                            children: \"Cidade (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 685,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"companyCity\",\n                                            name: \"companyCity\",\n                                            type: \"text\",\n                                            value: formData.companyCity,\n                                            onChange: handleChange,\n                                            className: inputClasses,\n                                            placeholder: \"Cidade\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 686,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 684,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyState\",\n                                            children: \"Estado (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 699,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"companyState\",\n                                            name: \"companyState\",\n                                            type: \"text\",\n                                            value: formData.companyState,\n                                            onChange: handleChange,\n                                            className: inputClasses,\n                                            placeholder: \"Estado\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 700,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 698,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyPostalCode\",\n                                            children: \"CEP (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 713,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_input_mask__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            mask: \"_____-___\",\n                                            replacement: {\n                                                _: /\\d/\n                                            },\n                                            name: \"companyPostalCode\",\n                                            value: formData.companyPostalCode,\n                                            onChange: handleChange,\n                                            className: inputClasses,\n                                            placeholder: \"00000-000\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 714,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 712,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 683,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between pt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handlePrevStep,\n                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 733,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Anterior\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 728,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleNextStep,\n                                    className: \"inline-flex items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600\",\n                                    children: [\n                                        \"Pr\\xf3ximo\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-4 h-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 743,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 737,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 727,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                    lineNumber: 575,\n                    columnNumber: 11\n                }, this),\n                currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        isDevelopment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-yellow-400\",\n                                            viewBox: \"0 0 20 20\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 758,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 757,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                        lineNumber: 756,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-yellow-800 dark:text-yellow-300\",\n                                                children: \"Modo Desenvolvimento Ativo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 762,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 text-sm text-yellow-700 dark:text-yellow-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Modo desenvolvimento ativo. Os dados dos formul\\xe1rios foram pr\\xe9-preenchidos para agilizar os testes. O fluxo de pagamento funcionar\\xe1 normalmente e voc\\xea ser\\xe1 redirecionado para o Stripe.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 765,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                        lineNumber: 761,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                lineNumber: 755,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 754,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"Escolha seu plano\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 777,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center items-center space-x-4 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm \".concat(billingCycle === 'monthly' ? 'text-orange-600 font-medium' : 'text-gray-500 dark:text-gray-400'),\n                                            children: \"Mensal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 779,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly'),\n                                            className: \"relative inline-flex h-6 w-11 items-center rounded-full transition-colors \".concat(billingCycle === 'yearly' ? 'bg-orange-500' : 'bg-gray-200 dark:bg-gray-600'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block h-4 w-4 transform rounded-full bg-white transition-transform \".concat(billingCycle === 'yearly' ? 'translate-x-6' : 'translate-x-1')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 789,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 782,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm \".concat(billingCycle === 'yearly' ? 'text-orange-600 font-medium' : 'text-gray-500 dark:text-gray-400'),\n                                            children: [\n                                                \"Anual \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-500 text-xs font-medium\",\n                                                    children: \"(2 meses gr\\xe1tis)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 796,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 795,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 778,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 776,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-700 rounded-xl border-2 border-gray-200 dark:border-gray-600 p-6 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                            children: \"Quantos usu\\xe1rios voc\\xea precisa?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 804,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: \"Selecione a quantidade de usu\\xe1rios que ter\\xe3o acesso ao sistema\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 807,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 803,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full max-w-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"1\",\n                                                    max: \"200\",\n                                                    value: userCount,\n                                                    onChange: (e)=>setUserCount(parseInt(e.target.value)),\n                                                    className: \"w-full h-2 rounded-lg appearance-none cursor-pointer\",\n                                                    style: {\n                                                        background: \"linear-gradient(to right, #f97316 0%, #f97316 \".concat(userCount / 200 * 100, \"%, #d1d5db \").concat(userCount / 200 * 100, \"%, #d1d5db 100%)\"),\n                                                        WebkitAppearance: 'none',\n                                                        outline: 'none'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 829,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 830,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 828,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 814,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setUserCount(Math.max(1, userCount - 1)),\n                                                    className: \"w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 flex items-center justify-center text-gray-700 dark:text-gray-300\",\n                                                    children: \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            min: \"1\",\n                                                            max: \"200\",\n                                                            value: userCount,\n                                                            onChange: (e)=>setUserCount(Math.max(1, Math.min(200, parseInt(e.target.value) || 1))),\n                                                            className: \"w-20 text-center text-2xl font-bold text-orange-600 dark:text-orange-400 bg-transparent border-none focus:outline-none\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 844,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                            children: \"usu\\xe1rios\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 852,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 843,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setUserCount(Math.min(200, userCount + 1)),\n                                                    className: \"w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 flex items-center justify-center text-gray-700 dark:text-gray-300\",\n                                                    children: \"+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 854,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 835,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 justify-center\",\n                                            children: [\n                                                5,\n                                                10,\n                                                20,\n                                                50,\n                                                100,\n                                                200\n                                            ].map((count)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setUserCount(count),\n                                                    className: \"px-3 py-1 rounded-full text-xs font-medium transition-colors \".concat(userCount === count ? 'bg-orange-500 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500'),\n                                                    children: count\n                                                }, count, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 866,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 864,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 812,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 802,\n                            columnNumber: 13\n                        }, this),\n                        (()=>{\n                            const pricing = (0,_utils_pricing__WEBPACK_IMPORTED_MODULE_7__.calculatePrice)(userCount);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 border-2 border-orange-200 dark:border-orange-700 rounded-xl p-6 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\",\n                                            children: \"Resumo do seu plano\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 889,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: \"Usu\\xe1rios:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 897,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                                    children: userCount\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 898,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 896,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: \"Pre\\xe7o por usu\\xe1rio:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 901,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        basePrice.toFixed(2).replace('.', ',')\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 902,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 900,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        pricing.discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: \"Desconto:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 906,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-green-600 dark:text-green-400\",\n                                                                    children: [\n                                                                        pricing.discount,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 907,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 905,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: \"Ciclo:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 911,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                                    children: billingCycle === 'monthly' ? 'Mensal' : 'Anual'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 912,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 910,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 895,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        (pricing.discount > 0 || billingCycle === 'yearly') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500 dark:text-gray-400 line-through\",\n                                                                    children: \"Valor sem desconto:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 922,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500 dark:text-gray-400 line-through\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        (billingCycle === 'monthly' ? pricing.totalWithoutDiscount : pricing.yearlyPriceWithoutAnnualDiscount).toFixed(2).replace('.', ',')\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 925,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 921,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                                    children: [\n                                                                        \"Valor \",\n                                                                        billingCycle === 'monthly' ? 'mensal' : 'anual',\n                                                                        \":\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 931,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-orange-600 dark:text-orange-400\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        (billingCycle === 'monthly' ? pricing.monthlyPrice : pricing.yearlyPrice).toFixed(2).replace('.', ',')\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 934,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 930,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        pricing.discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 px-3 py-1 rounded-full text-sm font-medium\",\n                                                                children: [\n                                                                    \"Economia de R$ \",\n                                                                    pricing.discountAmount.toFixed(2).replace('.', ','),\n                                                                    \" por m\\xeas (\",\n                                                                    pricing.discount,\n                                                                    \"% desconto)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                lineNumber: 942,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 941,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        billingCycle === 'yearly' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 px-3 py-1 rounded-full text-sm font-medium\",\n                                                                children: [\n                                                                    \"Economia anual de R$ \",\n                                                                    pricing.annualSavings.toFixed(2).replace('.', ','),\n                                                                    \" (2 meses gr\\xe1tis)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                lineNumber: 951,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 950,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 919,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 893,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white dark:bg-gray-800 rounded-lg p-4 border border-orange-200 dark:border-orange-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white mb-3\",\n                                                    children: \"Faixas de desconto:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 961,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 md:grid-cols-5 gap-2 text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded \".concat(userCount >= 5 && userCount < 20 ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300' : 'text-gray-600 dark:text-gray-400'),\n                                                            children: \"5-19 usu\\xe1rios: 10%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 963,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded \".concat(userCount >= 20 && userCount < 50 ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300' : 'text-gray-600 dark:text-gray-400'),\n                                                            children: \"20-49 usu\\xe1rios: 15%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 966,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded \".concat(userCount >= 50 && userCount < 100 ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300' : 'text-gray-600 dark:text-gray-400'),\n                                                            children: \"50-99 usu\\xe1rios: 25%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 969,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded \".concat(userCount >= 100 && userCount < 200 ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300' : 'text-gray-600 dark:text-gray-400'),\n                                                            children: \"100-199 usu\\xe1rios: 35%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 972,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded \".concat(userCount >= 200 ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300' : 'text-gray-600 dark:text-gray-400'),\n                                                            children: \"200+ usu\\xe1rios: 40%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 975,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 962,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 960,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 888,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                lineNumber: 887,\n                                columnNumber: 17\n                            }, this);\n                        })(),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 dark:bg-gray-700 rounded-xl p-6 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                    className: \"font-semibold text-gray-900 dark:text-white mb-3\",\n                                    children: \"Todos os planos incluem:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 987,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-500 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 990,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                    children: \"Sistema de Agendamento\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 991,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 989,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-500 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 994,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                    children: \"Gest\\xe3o de Pacientes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 995,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 993,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-500 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 998,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                    children: \"Calend\\xe1rio Integrado\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 999,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 997,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-500 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 1002,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                    children: \"Notifica\\xe7\\xf5es Autom\\xe1ticas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 1003,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 1001,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 988,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-3 bg-orange-100 dark:bg-orange-900/30 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-orange-800 dark:text-orange-300 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"M\\xf3dulos adicionais\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 1008,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" como Financeiro, RH e ABA+ podem ser contratados separadamente ap\\xf3s a assinatura.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                        lineNumber: 1007,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 1006,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 986,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between pt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handlePrevStep,\n                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 1019,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Anterior\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 1014,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleSubmit,\n                                    disabled: isLoading,\n                                    className: \"inline-flex items-center px-8 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-orange-500 hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 1031,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Processando...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            \"Finalizar e Pagar\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-5 h-5 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 1037,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 1023,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 1013,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                    lineNumber: 751,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n            lineNumber: 240,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n        lineNumber: 239,\n        columnNumber: 5\n    }, this);\n}\n_s(SubscriptionSignupPage, \"nplT6jpYkViVJh52yotzhTl1Kfg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SubscriptionSignupPage;\nvar _c;\n$RefreshReg$(_c, \"SubscriptionSignupPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/subscription/signup/page.js\n"));

/***/ })

});