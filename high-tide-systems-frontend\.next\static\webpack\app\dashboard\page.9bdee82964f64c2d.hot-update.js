"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/utils/api.js":
/*!**************************!*\
  !*** ./src/utils/api.js ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DOCUMENT_URLS: () => (/* binding */ DOCUMENT_URLS),\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// utils/api.js\n\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: 'http://localhost:5000',\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// Interceptor para adicionar token de autenticação em todas as requisições\napi.interceptors.request.use((config)=>{\n    const token = localStorage.getItem('token');\n    if (token) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>Promise.reject(error));\n// Interceptor para tratamento de erros\napi.interceptors.response.use((response)=>response, (error)=>{\n    var _error_response;\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n    }\n    return Promise.reject(error);\n});\n// Funções auxiliares para construir URLs\nconst getApiUrl = (path)=>{\n    // Remover barras iniciais extras se necessário\n    const cleanPath = path.startsWith('/') ? path.substring(1) : path;\n    return \"\".concat(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000', \"/\").concat(cleanPath);\n};\nconst DOCUMENT_URLS = {\n    get: (id)=>getApiUrl(\"documents/\".concat(id)),\n    download: (id)=>getApiUrl(\"documents/\".concat(id, \"?download=true\")),\n    upload: (targetType, targetId)=>getApiUrl(\"documents/upload?targetType=\".concat(targetType, \"&targetId=\").concat(targetId)),\n    list: (targetType, targetId)=>getApiUrl(\"documents?targetType=\".concat(targetType, \"&targetId=\").concat(targetId))\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/api.js\n"));

/***/ })

});