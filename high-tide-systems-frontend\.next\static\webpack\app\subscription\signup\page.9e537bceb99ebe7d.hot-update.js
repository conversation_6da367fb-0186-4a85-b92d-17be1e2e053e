"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/subscription/signup/page",{

/***/ "(app-pages-browser)/./src/app/subscription/signup/page.js":
/*!*********************************************!*\
  !*** ./src/app/subscription/signup/page.js ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SubscriptionSignupPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_input_mask__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @react-input/mask */ \"(app-pages-browser)/./node_modules/@react-input/mask/module/InputMask.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _services_subscriptionService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/subscriptionService */ \"(app-pages-browser)/./src/services/subscriptionService.js\");\n/* harmony import */ var _utils_pricing__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/pricing */ \"(app-pages-browser)/./src/utils/pricing.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SubscriptionSignupPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [documentType, setDocumentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"cpf\");\n    const [billingCycle, setBillingCycle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"monthly\");\n    const [userCount, setUserCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5); // Número de usuários selecionado\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Dados pessoais\n        login: \"\",\n        fullName: \"\",\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        cpf: \"\",\n        cnpj: \"\",\n        birthDate: \"\",\n        address: \"\",\n        phone: \"\",\n        // Dados da empresa\n        companyName: \"\",\n        companyTradingName: \"\",\n        companyCnpj: \"\",\n        companyPhone: \"\",\n        companyAddress: \"\",\n        companyCity: \"\",\n        companyState: \"\",\n        companyPostalCode: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Detecta se está em ambiente de desenvolvimento (localhost)\n    const isDevelopment =  true && (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');\n    // Dados de teste para desenvolvimento\n    const devData = {\n        login: \"teste_dev\",\n        fullName: \"Usuário de Teste\",\n        email: \"<EMAIL>\",\n        password: \"123456\",\n        confirmPassword: \"123456\",\n        cpf: \"123.456.789-00\",\n        cnpj: \"12.345.678/0001-90\",\n        birthDate: \"1990-01-01\",\n        address: \"Rua de Teste, 123\",\n        phone: \"(11) 99999-9999\",\n        companyName: \"Empresa de Teste Ltda\",\n        companyTradingName: \"Teste Corp\",\n        companyCnpj: \"98.765.432/0001-10\",\n        companyPhone: \"(11) 88888-8888\",\n        companyAddress: \"Av. Empresarial, 456\",\n        companyCity: \"São Paulo\",\n        companyState: \"SP\",\n        companyPostalCode: \"01234-567\"\n    };\n    // Função para calcular desconto baseado na quantidade de usuários\n    const getDiscountByUserCount = (users)=>{\n        if (users >= 200) return 40;\n        if (users >= 100) return 35;\n        if (users >= 50) return 25;\n        if (users >= 20) return 15;\n        if (users >= 5) return 10;\n        return 0;\n    };\n    // Função para calcular preços\n    const calculatePrice = (users)=>{\n        const discount = getDiscountByUserCount(users);\n        const totalWithoutDiscount = users * basePrice;\n        const discountAmount = totalWithoutDiscount * (discount / 100);\n        const finalPrice = totalWithoutDiscount - discountAmount;\n        // Calcular preço anual com desconto de 20% (equivalente a 10 meses)\n        const yearlyPriceWithoutAnnualDiscount = finalPrice * 12;\n        const yearlyPrice = finalPrice * 10; // 20% de desconto anual (2 meses grátis)\n        const annualSavings = yearlyPriceWithoutAnnualDiscount - yearlyPrice;\n        return {\n            totalWithoutDiscount,\n            discountAmount,\n            finalPrice,\n            discount,\n            monthlyPrice: finalPrice,\n            yearlyPrice,\n            yearlyPriceWithoutAnnualDiscount,\n            annualSavings\n        };\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Limpa o erro do campo quando o usuário começa a digitar\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: undefined\n                }));\n        }\n    };\n    const removeMask = (value)=>{\n        return value ? value.replace(/\\D/g, \"\") : \"\";\n    };\n    // Função para preencher dados de desenvolvimento\n    const fillDevData = ()=>{\n        if (isDevelopment) {\n            setFormData(devData);\n        }\n    };\n    // Função para pular para o próximo passo (apenas em desenvolvimento)\n    const skipToStep = (step)=>{\n        if (isDevelopment) {\n            if (step >= 2 && currentStep === 1) {\n                fillDevData();\n            }\n            setCurrentStep(step);\n        }\n    };\n    const validateStep1 = ()=>{\n        const newErrors = {};\n        if (!formData.login) newErrors.login = \"Login é obrigatório\";\n        if (!formData.fullName) newErrors.fullName = \"Nome é obrigatório\";\n        if (!formData.email || !/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = \"Email inválido\";\n        }\n        if (!formData.password || formData.password.length < 6) {\n            newErrors.password = \"Senha deve ter no mínimo 6 caracteres\";\n        }\n        if (formData.password !== formData.confirmPassword) {\n            newErrors.confirmPassword = \"Senhas não conferem\";\n        }\n        if (documentType === \"cpf\" && !formData.cpf) {\n            newErrors.cpf = \"CPF é obrigatório\";\n        }\n        if (documentType === \"cnpj\" && !formData.cnpj) {\n            newErrors.cnpj = \"CNPJ é obrigatório\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const validateStep2 = ()=>{\n        const newErrors = {};\n        if (!formData.companyName) newErrors.companyName = \"Nome da empresa é obrigatório\";\n        if (!formData.companyCnpj) newErrors.companyCnpj = \"CNPJ da empresa é obrigatório\";\n        if (!formData.companyPhone) newErrors.companyPhone = \"Telefone da empresa é obrigatório\";\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleNextStep = ()=>{\n        if (currentStep === 1 && validateStep1()) {\n            setCurrentStep(2);\n        } else if (currentStep === 2 && validateStep2()) {\n            setCurrentStep(3);\n        }\n    };\n    const handlePrevStep = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const handleSubmit = async ()=>{\n        if (!validateStep2()) return;\n        setIsLoading(true);\n        try {\n            // 1. Registra o usuário e empresa em uma única operação\n            const userResponse = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.post(\"/auth/register\", {\n                // Dados do usuário\n                login: formData.login,\n                fullName: formData.fullName,\n                email: formData.email,\n                password: formData.password,\n                cpf: documentType === \"cpf\" ? removeMask(formData.cpf) : undefined,\n                cnpj: documentType === \"cnpj\" ? removeMask(formData.cnpj) : undefined,\n                birthDate: formData.birthDate || undefined,\n                address: formData.address || undefined,\n                phone: formData.phone ? removeMask(formData.phone) : undefined,\n                // Dados da empresa\n                companyName: formData.companyName,\n                companyTradingName: formData.companyTradingName,\n                companyCnpj: formData.companyCnpj,\n                companyPhone: formData.companyPhone,\n                companyAddress: formData.companyAddress,\n                companyCity: formData.companyCity,\n                companyState: formData.companyState,\n                companyPostalCode: formData.companyPostalCode\n            });\n            // 2. Salva o token de autenticação\n            if (userResponse.data.token) {\n                localStorage.setItem('token', userResponse.data.token);\n            }\n            // 3. Cria a sessão de checkout do Stripe\n            const pricing = calculatePrice(userCount);\n            const checkoutResponse = await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_6__.subscriptionService.createCheckoutSession({\n                billingCycle,\n                users: userCount,\n                price: billingCycle === 'monthly' ? pricing.monthlyPrice : pricing.yearlyPrice,\n                discount: pricing.discount\n            });\n            // 4. Redireciona para o Stripe Checkout\n            if (checkoutResponse.url) {\n                window.location.href = checkoutResponse.url;\n            }\n        } catch (error) {\n            setErrors((prev)=>{\n                var _error_response_data, _error_response;\n                return {\n                    ...prev,\n                    submit: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Erro ao processar cadastro\"\n                };\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const inputClasses = \"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 dark:text-white bg-white dark:bg-gray-700\";\n    const labelClasses = \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\";\n    const iconContainerClasses = \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-xl w-full max-w-4xl p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-12 w-12 text-orange-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"mx-4 text-3xl font-bold text-gray-900 dark:text-white\",\n                                    children: \"Criar conta e assinar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center space-x-4 mb-6\",\n                            children: [\n                                1,\n                                2,\n                                3\n                            ].map((step)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium \".concat(currentStep >= step ? 'bg-orange-500 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300'),\n                                            children: currentStep > step ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 291,\n                                                columnNumber: 41\n                                            }, this) : step\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this),\n                                        step < 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-1 mx-2 \".concat(currentStep > step ? 'bg-orange-500' : 'bg-gray-200 dark:bg-gray-600')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 294,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, step, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                            children: [\n                                currentStep === 1 && \"Dados pessoais\",\n                                currentStep === 2 && \"Dados da empresa\",\n                                currentStep === 3 && \"Plano e pagamento\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this),\n                        isDevelopment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-yellow-800 dark:text-yellow-300 mb-2 text-center\",\n                                    children: \"\\uD83D\\uDEA7 Modo Desenvolvimento - Atalhos para testes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: fillDevData,\n                                            className: \"px-3 py-1 bg-yellow-500 text-white rounded text-xs hover:bg-yellow-600\",\n                                            children: \"Preencher Dados\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>skipToStep(1),\n                                            className: \"px-3 py-1 rounded text-xs \".concat(currentStep === 1 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                            children: \"Passo 1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 322,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>skipToStep(2),\n                                            className: \"px-3 py-1 rounded text-xs \".concat(currentStep === 2 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                            children: \"Passo 2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>skipToStep(3),\n                                            className: \"px-3 py-1 rounded text-xs \".concat(currentStep === 3 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                            children: \"Passo 3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 336,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, this),\n                errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg mb-6\",\n                    children: errors.submit\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                    lineNumber: 349,\n                    columnNumber: 11\n                }, this),\n                currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"login\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"login\",\n                                                    name: \"login\",\n                                                    type: \"text\",\n                                                    value: formData.login,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.login && \"border-red-500\"),\n                                                    placeholder: \"Seu login\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 361,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.login && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.login\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 377,\n                                            columnNumber: 34\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 359,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"fullName\",\n                                            children: \"Nome Completo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 382,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"fullName\",\n                                                    name: \"fullName\",\n                                                    type: \"text\",\n                                                    value: formData.fullName,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.fullName && \"border-red-500\"),\n                                                    placeholder: \"Seu nome completo\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.fullName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.fullName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 399,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 381,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"email\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 406,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"email\",\n                                                    name: \"email\",\n                                                    type: \"email\",\n                                                    value: formData.email,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.email && \"border-red-500\"),\n                                                    placeholder: \"<EMAIL>\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 407,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 423,\n                                            columnNumber: 34\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 405,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"password\",\n                                            children: \"Senha\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 427,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: \"password\",\n                                                    value: formData.password,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.password && \"border-red-500\"),\n                                                    placeholder: \"••••••••\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 428,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.password\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 444,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 426,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 404,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"confirmPassword\",\n                                            children: \"Confirmar Senha\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 451,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"confirmPassword\",\n                                                    name: \"confirmPassword\",\n                                                    type: \"password\",\n                                                    value: formData.confirmPassword,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.confirmPassword && \"border-red-500\"),\n                                                    placeholder: \"••••••••\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 452,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.confirmPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.confirmPassword\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 468,\n                                            columnNumber: 44\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 450,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            children: \"Tipo de Documento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 472,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"documentType\",\n                                                            value: \"cpf\",\n                                                            checked: documentType === \"cpf\",\n                                                            onChange: (e)=>setDocumentType(e.target.value),\n                                                            className: \"mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"CPF\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"documentType\",\n                                                            value: \"cnpj\",\n                                                            checked: documentType === \"cnpj\",\n                                                            onChange: (e)=>setDocumentType(e.target.value),\n                                                            className: \"mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"CNPJ\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 473,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 471,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 449,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: documentType,\n                                            children: documentType === \"cpf\" ? \"CPF\" : \"CNPJ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 503,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_input_mask__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    mask: documentType === \"cpf\" ? \"___.___.___-__\" : \"__.___.___/____-__\",\n                                                    replacement: {\n                                                        _: /\\d/\n                                                    },\n                                                    name: documentType,\n                                                    value: formData[documentType],\n                                                    onChange: handleChange,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors[documentType] && \"border-red-500\"),\n                                                    placeholder: documentType === \"cpf\" ? \"000.000.000-00\" : \"00.000.000/0000-00\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 506,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors[documentType] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors[documentType]\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 521,\n                                            columnNumber: 42\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 502,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"phone\",\n                                            children: \"Telefone (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 525,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_input_mask__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    mask: \"(__) _____-____\",\n                                                    replacement: {\n                                                        _: /\\d/\n                                                    },\n                                                    name: \"phone\",\n                                                    value: formData.phone,\n                                                    onChange: handleChange,\n                                                    className: inputClasses,\n                                                    placeholder: \"(11) 99999-9999\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 526,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 524,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 501,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"birthDate\",\n                                            children: \"Data de Nascimento (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 547,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"birthDate\",\n                                                    name: \"birthDate\",\n                                                    type: \"date\",\n                                                    value: formData.birthDate,\n                                                    onChange: handleChange,\n                                                    className: inputClasses,\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 548,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 546,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"address\",\n                                            children: \"Endere\\xe7o (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 565,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"address\",\n                                                    name: \"address\",\n                                                    type: \"text\",\n                                                    value: formData.address,\n                                                    onChange: handleChange,\n                                                    className: inputClasses,\n                                                    placeholder: \"Seu endere\\xe7o\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 566,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 564,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 545,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between pt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/landing\",\n                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 589,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Voltar\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 585,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleNextStep,\n                                    className: \"inline-flex items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600\",\n                                    children: [\n                                        \"Pr\\xf3ximo\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-4 h-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 599,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 593,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 584,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                    lineNumber: 356,\n                    columnNumber: 11\n                }, this),\n                currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyName\",\n                                            children: \"Nome da Empresa\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 611,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"companyName\",\n                                                    name: \"companyName\",\n                                                    type: \"text\",\n                                                    value: formData.companyName,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.companyName && \"border-red-500\"),\n                                                    placeholder: \"Raz\\xe3o social da empresa\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 612,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.companyName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.companyName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 628,\n                                            columnNumber: 40\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 610,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyTradingName\",\n                                            children: \"Nome Fantasia (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 632,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"companyTradingName\",\n                                                    name: \"companyTradingName\",\n                                                    type: \"text\",\n                                                    value: formData.companyTradingName,\n                                                    onChange: handleChange,\n                                                    className: inputClasses,\n                                                    placeholder: \"Nome fantasia\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 633,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 631,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 609,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyCnpj\",\n                                            children: \"CNPJ da Empresa\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 654,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_input_mask__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    mask: \"__.___.___/____-__\",\n                                                    replacement: {\n                                                        _: /\\d/\n                                                    },\n                                                    name: \"companyCnpj\",\n                                                    value: formData.companyCnpj,\n                                                    onChange: handleChange,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.companyCnpj && \"border-red-500\"),\n                                                    placeholder: \"00.000.000/0000-00\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 659,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 655,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.companyCnpj && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.companyCnpj\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 670,\n                                            columnNumber: 40\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 653,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyPhone\",\n                                            children: \"Telefone da Empresa\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 674,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_input_mask__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    mask: \"(__) _____-____\",\n                                                    replacement: {\n                                                        _: /\\d/\n                                                    },\n                                                    name: \"companyPhone\",\n                                                    value: formData.companyPhone,\n                                                    onChange: handleChange,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.companyPhone && \"border-red-500\"),\n                                                    placeholder: \"(11) 99999-9999\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 675,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.companyPhone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.companyPhone\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 690,\n                                            columnNumber: 41\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 673,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 652,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: labelClasses,\n                                    htmlFor: \"companyAddress\",\n                                    children: \"Endere\\xe7o da Empresa (opcional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 696,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: iconContainerClasses,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 699,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 698,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"companyAddress\",\n                                            name: \"companyAddress\",\n                                            type: \"text\",\n                                            value: formData.companyAddress,\n                                            onChange: handleChange,\n                                            className: inputClasses,\n                                            placeholder: \"Endere\\xe7o completo da empresa\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 701,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 697,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 695,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyCity\",\n                                            children: \"Cidade (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 717,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"companyCity\",\n                                            name: \"companyCity\",\n                                            type: \"text\",\n                                            value: formData.companyCity,\n                                            onChange: handleChange,\n                                            className: inputClasses,\n                                            placeholder: \"Cidade\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 718,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 716,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyState\",\n                                            children: \"Estado (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 731,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"companyState\",\n                                            name: \"companyState\",\n                                            type: \"text\",\n                                            value: formData.companyState,\n                                            onChange: handleChange,\n                                            className: inputClasses,\n                                            placeholder: \"Estado\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 732,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 730,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyPostalCode\",\n                                            children: \"CEP (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 745,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_input_mask__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            mask: \"_____-___\",\n                                            replacement: {\n                                                _: /\\d/\n                                            },\n                                            name: \"companyPostalCode\",\n                                            value: formData.companyPostalCode,\n                                            onChange: handleChange,\n                                            className: inputClasses,\n                                            placeholder: \"00000-000\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 746,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 744,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 715,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between pt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handlePrevStep,\n                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 765,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Anterior\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 760,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleNextStep,\n                                    className: \"inline-flex items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600\",\n                                    children: [\n                                        \"Pr\\xf3ximo\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-4 h-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 775,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 769,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 759,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                    lineNumber: 607,\n                    columnNumber: 11\n                }, this),\n                currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        isDevelopment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-yellow-400\",\n                                            viewBox: \"0 0 20 20\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 790,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 789,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                        lineNumber: 788,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-yellow-800 dark:text-yellow-300\",\n                                                children: \"Modo Desenvolvimento Ativo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 794,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 text-sm text-yellow-700 dark:text-yellow-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Modo desenvolvimento ativo. Os dados dos formul\\xe1rios foram pr\\xe9-preenchidos para agilizar os testes. O fluxo de pagamento funcionar\\xe1 normalmente e voc\\xea ser\\xe1 redirecionado para o Stripe.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 797,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                        lineNumber: 793,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                lineNumber: 787,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 786,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"Escolha seu plano\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 809,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center items-center space-x-4 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm \".concat(billingCycle === 'monthly' ? 'text-orange-600 font-medium' : 'text-gray-500 dark:text-gray-400'),\n                                            children: \"Mensal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 811,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly'),\n                                            className: \"relative inline-flex h-6 w-11 items-center rounded-full transition-colors \".concat(billingCycle === 'yearly' ? 'bg-orange-500' : 'bg-gray-200 dark:bg-gray-600'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block h-4 w-4 transform rounded-full bg-white transition-transform \".concat(billingCycle === 'yearly' ? 'translate-x-6' : 'translate-x-1')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 821,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 814,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm \".concat(billingCycle === 'yearly' ? 'text-orange-600 font-medium' : 'text-gray-500 dark:text-gray-400'),\n                                            children: [\n                                                \"Anual \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-500 text-xs font-medium\",\n                                                    children: \"(2 meses gr\\xe1tis)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 828,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 827,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 810,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 808,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-700 rounded-xl border-2 border-gray-200 dark:border-gray-600 p-6 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                            children: \"Quantos usu\\xe1rios voc\\xea precisa?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 836,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: \"Selecione a quantidade de usu\\xe1rios que ter\\xe3o acesso ao sistema\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 839,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 835,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full max-w-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"1\",\n                                                    max: \"200\",\n                                                    value: userCount,\n                                                    onChange: (e)=>setUserCount(parseInt(e.target.value)),\n                                                    className: \"w-full h-2 rounded-lg appearance-none cursor-pointer\",\n                                                    style: {\n                                                        background: \"linear-gradient(to right, #f97316 0%, #f97316 \".concat(userCount / 200 * 100, \"%, #d1d5db \").concat(userCount / 200 * 100, \"%, #d1d5db 100%)\"),\n                                                        WebkitAppearance: 'none',\n                                                        outline: 'none'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 847,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 861,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 862,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 860,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 846,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setUserCount(Math.max(1, userCount - 1)),\n                                                    className: \"w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 flex items-center justify-center text-gray-700 dark:text-gray-300\",\n                                                    children: \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 868,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            min: \"1\",\n                                                            max: \"200\",\n                                                            value: userCount,\n                                                            onChange: (e)=>setUserCount(Math.max(1, Math.min(200, parseInt(e.target.value) || 1))),\n                                                            className: \"w-20 text-center text-2xl font-bold text-orange-600 dark:text-orange-400 bg-transparent border-none focus:outline-none\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 876,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                            children: \"usu\\xe1rios\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 884,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 875,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setUserCount(Math.min(200, userCount + 1)),\n                                                    className: \"w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 flex items-center justify-center text-gray-700 dark:text-gray-300\",\n                                                    children: \"+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 886,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 867,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 justify-center\",\n                                            children: [\n                                                5,\n                                                10,\n                                                20,\n                                                50,\n                                                100,\n                                                200\n                                            ].map((count)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setUserCount(count),\n                                                    className: \"px-3 py-1 rounded-full text-xs font-medium transition-colors \".concat(userCount === count ? 'bg-orange-500 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500'),\n                                                    children: count\n                                                }, count, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 898,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 896,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 844,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 834,\n                            columnNumber: 13\n                        }, this),\n                        (()=>{\n                            const pricing = calculatePrice(userCount);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 border-2 border-orange-200 dark:border-orange-700 rounded-xl p-6 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\",\n                                            children: \"Resumo do seu plano\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 921,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: \"Usu\\xe1rios:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 929,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                                    children: userCount\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 930,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 928,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: \"Pre\\xe7o por usu\\xe1rio:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 933,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        basePrice.toFixed(2).replace('.', ',')\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 934,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 932,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        pricing.discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: \"Desconto:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 938,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-green-600 dark:text-green-400\",\n                                                                    children: [\n                                                                        pricing.discount,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 939,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 937,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: \"Ciclo:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 943,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                                    children: billingCycle === 'monthly' ? 'Mensal' : 'Anual'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 944,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 942,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 927,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        (pricing.discount > 0 || billingCycle === 'yearly') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500 dark:text-gray-400 line-through\",\n                                                                    children: \"Valor sem desconto:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 954,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500 dark:text-gray-400 line-through\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        (billingCycle === 'monthly' ? pricing.totalWithoutDiscount : pricing.yearlyPriceWithoutAnnualDiscount).toFixed(2).replace('.', ',')\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 957,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 953,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                                    children: [\n                                                                        \"Valor \",\n                                                                        billingCycle === 'monthly' ? 'mensal' : 'anual',\n                                                                        \":\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 963,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-orange-600 dark:text-orange-400\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        (billingCycle === 'monthly' ? pricing.monthlyPrice : pricing.yearlyPrice).toFixed(2).replace('.', ',')\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 966,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 962,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        pricing.discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 px-3 py-1 rounded-full text-sm font-medium\",\n                                                                children: [\n                                                                    \"Economia de R$ \",\n                                                                    pricing.discountAmount.toFixed(2).replace('.', ','),\n                                                                    \" por m\\xeas (\",\n                                                                    pricing.discount,\n                                                                    \"% desconto)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                lineNumber: 974,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 973,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        billingCycle === 'yearly' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 px-3 py-1 rounded-full text-sm font-medium\",\n                                                                children: [\n                                                                    \"Economia anual de R$ \",\n                                                                    pricing.annualSavings.toFixed(2).replace('.', ','),\n                                                                    \" (2 meses gr\\xe1tis)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                lineNumber: 983,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 982,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 951,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 925,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white dark:bg-gray-800 rounded-lg p-4 border border-orange-200 dark:border-orange-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white mb-3\",\n                                                    children: \"Faixas de desconto:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 993,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 md:grid-cols-5 gap-2 text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded \".concat(userCount >= 5 && userCount < 20 ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300' : 'text-gray-600 dark:text-gray-400'),\n                                                            children: \"5-19 usu\\xe1rios: 10%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 995,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded \".concat(userCount >= 20 && userCount < 50 ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300' : 'text-gray-600 dark:text-gray-400'),\n                                                            children: \"20-49 usu\\xe1rios: 15%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 998,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded \".concat(userCount >= 50 && userCount < 100 ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300' : 'text-gray-600 dark:text-gray-400'),\n                                                            children: \"50-99 usu\\xe1rios: 25%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 1001,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded \".concat(userCount >= 100 && userCount < 200 ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300' : 'text-gray-600 dark:text-gray-400'),\n                                                            children: \"100-199 usu\\xe1rios: 35%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 1004,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded \".concat(userCount >= 200 ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300' : 'text-gray-600 dark:text-gray-400'),\n                                                            children: \"200+ usu\\xe1rios: 40%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 1007,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 994,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 992,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 920,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                lineNumber: 919,\n                                columnNumber: 17\n                            }, this);\n                        })(),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 dark:bg-gray-700 rounded-xl p-6 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                    className: \"font-semibold text-gray-900 dark:text-white mb-3\",\n                                    children: \"Todos os planos incluem:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 1019,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-500 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 1022,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                    children: \"Sistema de Agendamento\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 1023,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 1021,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-500 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 1026,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                    children: \"Gest\\xe3o de Pacientes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 1027,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 1025,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-500 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 1030,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                    children: \"Calend\\xe1rio Integrado\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 1031,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 1029,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-500 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 1034,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                    children: \"Notifica\\xe7\\xf5es Autom\\xe1ticas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 1035,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 1033,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 1020,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-3 bg-orange-100 dark:bg-orange-900/30 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-orange-800 dark:text-orange-300 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"M\\xf3dulos adicionais\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 1040,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" como Financeiro, RH e ABA+ podem ser contratados separadamente ap\\xf3s a assinatura.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                        lineNumber: 1039,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 1038,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 1018,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between pt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handlePrevStep,\n                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 1051,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Anterior\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 1046,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleSubmit,\n                                    disabled: isLoading,\n                                    className: \"inline-flex items-center px-8 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-orange-500 hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 1063,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Processando...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            \"Finalizar e Pagar\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-5 h-5 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 1069,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 1055,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 1045,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                    lineNumber: 783,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n            lineNumber: 272,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, this);\n}\n_s(SubscriptionSignupPage, \"nplT6jpYkViVJh52yotzhTl1Kfg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SubscriptionSignupPage;\nvar _c;\n$RefreshReg$(_c, \"SubscriptionSignupPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/subscription/signup/page.js\n"));

/***/ })

});