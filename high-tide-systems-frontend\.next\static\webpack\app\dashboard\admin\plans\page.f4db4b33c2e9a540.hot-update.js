"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/plans/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js":
/*!**************************************************!*\
  !*** ./src/app/modules/admin/plans/PlansPage.js ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ban.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/modules/admin/services/plansService */ \"(app-pages-browser)/./src/app/modules/admin/services/plansService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst PlansPage = ()=>{\n    var _selectedModule_info_monthlyPrice, _selectedModule_info, _selectedModule_info_pricePerMonth, _selectedModule_info1, _selectedModule_info2, _selectedModule_info3;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { can } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions)();\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const [planData, setPlanData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [availablePlans, setAvailablePlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCompanyId, setSelectedCompanyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para o modal de adicionar usuários\n    const [showAddUsersModal, setShowAddUsersModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [additionalUsersCount, setAdditionalUsersCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cancelConfirmationText, setCancelConfirmationText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Estados para o modal de confirmação de módulos\n    const [showModuleConfirmModal, setShowModuleConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [moduleAction, setModuleAction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // 'add' ou 'remove'\n    const [selectedModule, setSelectedModule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Verificar se o usuário atual é um system_admin\n    const isSystemAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    // Função para carregar empresas (apenas para system_admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const response = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_7__.companyService.getCompaniesForSelect();\n            setCompanies(response);\n            // Se não há empresa selecionada e há empresas disponíveis, selecionar a primeira\n            if (!selectedCompanyId && response.length > 0) {\n                setSelectedCompanyId(response[0].id);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível carregar as empresas.\"\n            });\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    // Função para carregar dados do plano\n    const loadPlanData = async function() {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        console.log('[DEBUG] ===== INICIANDO loadPlanData =====');\n        console.log('[DEBUG] forceRefresh:', forceRefresh);\n        console.log('[DEBUG] isSystemAdmin:', isSystemAdmin);\n        console.log('[DEBUG] selectedCompanyId:', selectedCompanyId);\n        setIsLoading(true);\n        try {\n            var _planResponse_modules, _planResponse_modules1, _planData_modules;\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            console.log('[DEBUG] Carregando dados do plano para empresa:', companyId, 'forceRefresh:', forceRefresh);\n            console.log('[DEBUG] Fazendo chamadas para API...');\n            const [planResponse, availablePlansResponse] = await Promise.all([\n                _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_6__.plansService.getPlansData(companyId, forceRefresh),\n                _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_6__.plansService.getAvailablePlans()\n            ]);\n            console.log('[DEBUG] ===== RESPOSTA RECEBIDA =====');\n            console.log('[DEBUG] planResponse completo:', JSON.stringify(planResponse, null, 2));\n            console.log('[DEBUG] availablePlansResponse completo:', JSON.stringify(availablePlansResponse, null, 2));\n            console.log('[DEBUG] Módulos ativos:', planResponse === null || planResponse === void 0 ? void 0 : (_planResponse_modules = planResponse.modules) === null || _planResponse_modules === void 0 ? void 0 : _planResponse_modules.map((m)=>\"\".concat(m.moduleType, \" (\").concat(m.active ? 'ATIVO' : 'INATIVO', \")\")));\n            console.log('[DEBUG] Quantidade de módulos:', planResponse === null || planResponse === void 0 ? void 0 : (_planResponse_modules1 = planResponse.modules) === null || _planResponse_modules1 === void 0 ? void 0 : _planResponse_modules1.length);\n            console.log('[DEBUG] availablePlans.modules:', availablePlansResponse === null || availablePlansResponse === void 0 ? void 0 : availablePlansResponse.modules);\n            console.log('[DEBUG] ===== ATUALIZANDO ESTADO =====');\n            console.log('[DEBUG] Estado anterior planData:', planData === null || planData === void 0 ? void 0 : (_planData_modules = planData.modules) === null || _planData_modules === void 0 ? void 0 : _planData_modules.map((m)=>m.moduleType));\n            setPlanData(planResponse);\n            setAvailablePlans(availablePlansResponse);\n            console.log('[DEBUG] ===== ESTADO ATUALIZADO =====');\n        } catch (error) {\n            var _error_response;\n            console.error(\"[DEBUG] ===== ERRO AO CARREGAR DADOS =====\");\n            console.error(\"Erro ao carregar dados do plano:\", error);\n            console.error(\"Error details:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível carregar os dados do plano.\"\n            });\n        } finally{\n            setIsLoading(false);\n            console.log('[DEBUG] ===== FIM loadPlanData =====');\n        }\n    };\n    // Carregar dados iniciais\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (isSystemAdmin) {\n                loadCompanies();\n            } else {\n                loadPlanData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        isSystemAdmin\n    ]);\n    // Recarregar dados quando a empresa selecionada mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (isSystemAdmin && selectedCompanyId) {\n                loadPlanData();\n            } else if (!isSystemAdmin) {\n                loadPlanData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        selectedCompanyId,\n        isSystemAdmin\n    ]);\n    // Monitor planData changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            var _planData_modules;\n            console.log('[DEBUG] ===== PLANDATA MUDOU =====');\n            console.log('[DEBUG] planData:', planData);\n            console.log('[DEBUG] Módulos no estado:', planData === null || planData === void 0 ? void 0 : (_planData_modules = planData.modules) === null || _planData_modules === void 0 ? void 0 : _planData_modules.map({\n                \"PlansPage.useEffect\": (m)=>\"\".concat(m.moduleType, \" (\").concat(m.active ? 'ATIVO' : 'INATIVO', \")\")\n            }[\"PlansPage.useEffect\"]));\n            console.log('[DEBUG] ================================');\n        }\n    }[\"PlansPage.useEffect\"], [\n        planData\n    ]);\n    // Função para abrir modal de adicionar usuários\n    const handleOpenAddUsersModal = ()=>{\n        setAdditionalUsersCount(1);\n        setShowAddUsersModal(true);\n    };\n    // Função para fechar modal de adicionar usuários\n    const handleCloseAddUsersModal = ()=>{\n        setShowAddUsersModal(false);\n        setAdditionalUsersCount(1);\n    };\n    // Função para abrir modal de cancelamento\n    const handleOpenCancelModal = ()=>{\n        setCancelConfirmationText('');\n        setShowCancelModal(true);\n    };\n    // Função para fechar modal de cancelamento\n    const handleCloseCancelModal = ()=>{\n        setShowCancelModal(false);\n        setCancelConfirmationText('');\n    };\n    // Função para calcular o preço adicional por usuário (baseado no preço atual)\n    const calculatePricePerUser = ()=>{\n        if (!planData) return 19.90; // Preço padrão\n        // Calcular preço por usuário baseado no plano atual\n        const currentPrice = planData.subscription.pricePerMonth;\n        const currentUsers = planData.subscription.userLimit;\n        if (currentUsers > 0) {\n            return currentPrice / currentUsers;\n        }\n        return 19.90; // Preço padrão se não conseguir calcular\n    };\n    // Função para calcular o custo adicional\n    const calculateAdditionalCost = ()=>{\n        const pricePerUser = calculatePricePerUser();\n        return pricePerUser * additionalUsersCount;\n    };\n    // Função para adicionar usuários (confirmada pelo modal)\n    const handleAddUsers = async ()=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_6__.plansService.addUsers(additionalUsersCount, companyId);\n            const additionalCost = calculateAdditionalCost();\n            toast_success({\n                title: \"Usuários Adicionados\",\n                message: \"\".concat(additionalUsersCount, \" usu\\xe1rio(s) adicionado(s) ao plano. Custo adicional: R$ \").concat(additionalCost.toFixed(2), \"/m\\xeas.\")\n            });\n            handleCloseAddUsersModal();\n            loadPlanData(true); // Force refresh\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Erro ao adicionar usuários:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Não foi possível adicionar usuários ao plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para abrir modal de confirmação para adicionar módulo\n    const openAddModuleConfirmation = (moduleType)=>{\n        console.log('[DEBUG] openAddModuleConfirmation:', {\n            moduleType,\n            availablePlans\n        });\n        console.log('[DEBUG] availablePlans.modules:', availablePlans === null || availablePlans === void 0 ? void 0 : availablePlans.modules);\n        if (!(availablePlans === null || availablePlans === void 0 ? void 0 : availablePlans.modules)) {\n            console.error('[DEBUG] availablePlans.modules não está disponível');\n            toast_error({\n                title: \"Erro\",\n                message: \"Dados dos módulos não estão disponíveis. Tente recarregar a página.\"\n            });\n            return;\n        }\n        const moduleInfo = availablePlans.modules[moduleType];\n        console.log('[DEBUG] moduleInfo encontrado:', moduleInfo);\n        setSelectedModule({\n            type: moduleType,\n            info: moduleInfo\n        });\n        setModuleAction('add');\n        setShowModuleConfirmModal(true);\n    };\n    // Função para abrir modal de confirmação para remover módulo\n    const openRemoveModuleConfirmation = (moduleType)=>{\n        var _planData_modules;\n        const moduleInfo = planData === null || planData === void 0 ? void 0 : (_planData_modules = planData.modules) === null || _planData_modules === void 0 ? void 0 : _planData_modules.find((m)=>m.moduleType === moduleType);\n        setSelectedModule({\n            type: moduleType,\n            info: moduleInfo\n        });\n        setModuleAction('remove');\n        setShowModuleConfirmModal(true);\n    };\n    // Função para fechar modal de confirmação de módulos\n    const closeModuleConfirmModal = ()=>{\n        setShowModuleConfirmModal(false);\n        setModuleAction(null);\n        setSelectedModule(null);\n    };\n    // Função para confirmar a ação do módulo\n    const confirmModuleAction = async ()=>{\n        if (!selectedModule || !moduleAction) return;\n        closeModuleConfirmModal();\n        if (moduleAction === 'add') {\n            await handleAddModule(selectedModule.type);\n        } else if (moduleAction === 'remove') {\n            await handleRemoveModule(selectedModule.type);\n        }\n    };\n    // Função para adicionar módulo\n    const handleAddModule = async (moduleType)=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_6__.plansService.addModule(moduleType, companyId);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Módulo adicionado ao plano com sucesso.\"\n            });\n            // Aguardar um pouco para garantir que o cache foi invalidado\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            await loadPlanData(true); // Force refresh\n        } catch (error) {\n            console.error(\"Erro ao adicionar módulo:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível adicionar o módulo ao plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para remover módulo\n    const handleRemoveModule = async (moduleType)=>{\n        console.log('[DEBUG] Iniciando remoção do módulo:', moduleType);\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            console.log('[DEBUG] Removendo módulo para empresa:', companyId);\n            const result = await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_6__.plansService.removeModule(moduleType, companyId);\n            console.log('[DEBUG] Resultado da remoção:', result);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Módulo removido do plano com sucesso.\"\n            });\n            console.log('[DEBUG] Aguardando invalidação de cache...');\n            // Aguardar um pouco para garantir que o cache foi invalidado\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            console.log('[DEBUG] Recarregando dados do plano...');\n            await loadPlanData(true); // Force refresh para evitar cache\n            console.log('[DEBUG] Dados recarregados');\n        } catch (error) {\n            console.error(\"Erro ao remover módulo:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível remover o módulo do plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para cancelar assinatura (confirmada pelo modal)\n    const handleCancelSubscription = async ()=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_6__.plansService.cancelSubscription(companyId);\n            toast_success({\n                title: \"Assinatura Cancelada\",\n                message: \"Sua assinatura foi cancelada com sucesso. O acesso será mantido até o final do período pago.\"\n            });\n            handleCloseCancelModal();\n            loadPlanData(true); // Force refresh\n        } catch (error) {\n            console.error(\"Erro ao cancelar assinatura:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível cancelar a assinatura.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para reativar assinatura\n    const handleReactivateSubscription = async ()=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_6__.plansService.reactivateSubscription(companyId);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Assinatura reativada com sucesso.\"\n            });\n            loadPlanData();\n        } catch (error) {\n            console.error(\"Erro ao reativar assinatura:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível reativar a assinatura.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para formatar status\n    const getStatusInfo = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return {\n                    label: 'Ativo',\n                    color: 'text-green-600 dark:text-green-400',\n                    bgColor: 'bg-green-100 dark:bg-green-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                };\n            case 'CANCELED':\n                return {\n                    label: 'Cancelado',\n                    color: 'text-red-600 dark:text-red-400',\n                    bgColor: 'bg-red-100 dark:bg-red-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                };\n            case 'PAST_DUE':\n                return {\n                    label: 'Em Atraso',\n                    color: 'text-yellow-600 dark:text-yellow-400',\n                    bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                };\n            default:\n                return {\n                    label: status,\n                    color: 'text-gray-600 dark:text-gray-400',\n                    bgColor: 'bg-gray-100 dark:bg-gray-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                };\n        }\n    };\n    // Função para formatar ciclo de cobrança\n    const getBillingCycleLabel = (cycle)=>{\n        switch(cycle){\n            case 'MONTHLY':\n                return 'Mensal';\n            case 'YEARLY':\n                return 'Anual';\n            default:\n                return cycle;\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"animate-spin h-8 w-8 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 434,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2 text-gray-600 dark:text-gray-400\",\n                    children: \"Carregando dados do plano...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 435,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 433,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Mostrar mensagem para system_admin quando nenhuma empresa está selecionada\n    if (isSystemAdmin && !selectedCompanyId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        size: 22,\n                        className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 446,\n                        columnNumber: 17\n                    }, void 0),\n                    description: \"Gerencie planos, usu\\xe1rios e m\\xf3dulos das assinaturas das empresas.\",\n                    moduleColor: \"admin\",\n                    filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full sm:w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleSelect, {\n                            moduleColor: \"admin\",\n                            value: selectedCompanyId,\n                            onChange: (e)=>setSelectedCompanyId(e.target.value),\n                            placeholder: \"Selecione uma empresa\",\n                            disabled: isLoadingCompanies,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Selecione uma empresa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 458,\n                                    columnNumber: 17\n                                }, void 0),\n                                companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: company.id,\n                                        children: company.name\n                                    }, company.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 460,\n                                        columnNumber: 19\n                                    }, void 0))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 451,\n                            columnNumber: 15\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 450,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 444,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 470,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            children: \"Selecione uma empresa\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 471,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"Escolha uma empresa no seletor acima para visualizar e gerenciar seu plano.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 474,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 469,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 443,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!planData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        size: 22,\n                        className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 487,\n                        columnNumber: 17\n                    }, void 0),\n                    description: \"Gerencie seu plano, usu\\xe1rios e m\\xf3dulos da assinatura.\",\n                    moduleColor: \"admin\",\n                    filters: isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full sm:w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleSelect, {\n                            moduleColor: \"admin\",\n                            value: selectedCompanyId,\n                            onChange: (e)=>setSelectedCompanyId(e.target.value),\n                            placeholder: \"Selecione uma empresa\",\n                            disabled: isLoadingCompanies,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Selecione uma empresa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 500,\n                                    columnNumber: 19\n                                }, void 0),\n                                companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: company.id,\n                                        children: company.name\n                                    }, company.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 502,\n                                        columnNumber: 21\n                                    }, void 0))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 493,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 492,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 485,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 513,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            children: \"Nenhum plano encontrado\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 514,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"N\\xe3o foi poss\\xedvel encontrar informa\\xe7\\xf5es do plano para esta empresa.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 517,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 512,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 484,\n            columnNumber: 7\n        }, undefined);\n    }\n    const statusInfo = getStatusInfo(planData.subscription.status);\n    const StatusIcon = statusInfo.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleHeader, {\n                title: \"Gerenciamento de Planos\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 533,\n                    columnNumber: 15\n                }, void 0),\n                description: isSystemAdmin ? \"Gerencie o plano, usu\\xe1rios e m\\xf3dulos da assinatura de \".concat(planData.company.name, \".\") : \"Gerencie seu plano, usuários e módulos da assinatura.\",\n                moduleColor: \"admin\",\n                filters: isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full sm:w-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleSelect, {\n                        moduleColor: \"admin\",\n                        value: selectedCompanyId,\n                        onChange: (e)=>setSelectedCompanyId(e.target.value),\n                        placeholder: \"Selecione uma empresa\",\n                        disabled: isLoadingCompanies,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"Selecione uma empresa\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 549,\n                                columnNumber: 17\n                            }, void 0),\n                            companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: company.id,\n                                    children: company.name\n                                }, company.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 551,\n                                    columnNumber: 19\n                                }, void 0))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 542,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 541,\n                    columnNumber: 13\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 531,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 text-yellow-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 567,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Plano Atual\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 566,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(statusInfo.bgColor, \" \").concat(statusInfo.color),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 571,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            statusInfo.label\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 570,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 565,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Empresa\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: planData.company.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 578,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Ciclo de Cobran\\xe7a\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: getBillingCycleLabel(planData.subscription.billingCycle)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 584,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 577,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pre\\xe7o Mensal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-gray-900 dark:text-gray-100\",\n                                                        children: [\n                                                            \"R$ \",\n                                                            planData.subscription.pricePerMonth.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 593,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pr\\xf3xima Cobran\\xe7a\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: planData.subscription.nextBillingDate ? new Date(planData.subscription.nextBillingDate).toLocaleDateString('pt-BR') : 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 599,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 592,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 pt-4 border-t border-gray-200 dark:border-gray-700\",\n                                        children: [\n                                            planData.subscription.status === 'ACTIVE' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleOpenCancelModal,\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Cancelar Plano\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 613,\n                                                columnNumber: 17\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleReactivateSubscription,\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Reativar Plano\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 622,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    const url = isSystemAdmin && selectedCompanyId ? \"/subscription/invoices?companyId=\".concat(selectedCompanyId) : '/subscription/invoices';\n                                                    router.push(url);\n                                                },\n                                                className: \"flex items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 641,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Ver Faturas\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 632,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 611,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 576,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 564,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 651,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Usu\\xe1rios\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 650,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Uso atual\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 658,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            planData.usage.currentUsers,\n                                                            \" / \",\n                                                            planData.usage.userLimit\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 657,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\",\n                                                    style: {\n                                                        width: \"\".concat(Math.min(planData.usage.userLimitUsage, 100), \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 661,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                                children: [\n                                                    planData.usage.userLimitUsage,\n                                                    \"% utilizado\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 667,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 656,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                                children: [\n                                                    planData.usage.availableUsers,\n                                                    \" usu\\xe1rios dispon\\xedveis\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 673,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleOpenAddUsersModal,\n                                                disabled: isUpdating,\n                                                className: \"w-full flex items-center justify-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Adicionar Usu\\xe1rios\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 676,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 672,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 655,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 649,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 562,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"mr-2 h-5 w-5 text-purple-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 692,\n                                columnNumber: 11\n                            }, undefined),\n                            \"M\\xf3dulos da Assinatura\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 691,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: [\n                            planData.modules.map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-green-200 dark:border-green-800 rounded-lg p-4 bg-green-50 dark:bg-green-900/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-500 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                            children: getModuleName(module.moduleType)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 703,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-green-600 dark:text-green-400 font-medium\",\n                                                    children: \"Ativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 700,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: [\n                                                \"R$ \",\n                                                module.pricePerMonth.toFixed(2),\n                                                \"/m\\xeas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 711,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                            children: [\n                                                \"Adicionado em \",\n                                                new Date(module.addedAt).toLocaleDateString('pt-BR')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 714,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        !isBasicModule(module.moduleType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>openRemoveModuleConfirmation(module.moduleType),\n                                            disabled: isUpdating,\n                                            className: \"mt-3 w-full flex items-center justify-center px-2 py-1 bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:hover:bg-red-900/50 text-red-700 dark:text-red-400 text-xs font-medium rounded transition-colors disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"mr-1 h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Remover\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 720,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, module.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 699,\n                                    columnNumber: 13\n                                }, undefined)),\n                            availablePlans && Object.entries(availablePlans.modules).filter((param)=>{\n                                let [moduleType, moduleInfo] = param;\n                                return !planData.modules.some((m)=>m.moduleType === moduleType) && !moduleInfo.included;\n                            }).map((param)=>{\n                                let [moduleType, moduleInfo] = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-900/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                            children: moduleInfo.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 743,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400 font-medium\",\n                                                    children: \"Dispon\\xedvel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 747,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 740,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: moduleInfo.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 751,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-3\",\n                                            children: [\n                                                \"R$ \",\n                                                moduleInfo.monthlyPrice.toFixed(2),\n                                                \"/m\\xeas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 754,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>openAddModuleConfirmation(moduleType),\n                                            disabled: isUpdating,\n                                            className: \"w-full flex items-center justify-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded transition-colors disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"mr-1 h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 763,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Adicionar\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 758,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, moduleType, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 739,\n                                    columnNumber: 15\n                                }, undefined);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 696,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 690,\n                columnNumber: 7\n            }, undefined),\n            showModuleConfirmModal && selectedModule && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50\",\n                        onClick: closeModuleConfirmModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 775,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full mx-4 z-[11050]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center w-12 h-12 rounded-full mr-4 \".concat(moduleAction === 'add' ? 'bg-blue-100 dark:bg-blue-900/30' : 'bg-red-100 dark:bg-red-900/30'),\n                                                    children: moduleAction === 'add' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-6 w-6 \".concat(moduleAction === 'add' ? 'text-blue-600 dark:text-blue-400' : 'text-red-600 dark:text-red-400')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 23\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-6 w-6 \".concat(moduleAction === 'add' ? 'text-blue-600 dark:text-blue-400' : 'text-red-600 dark:text-red-400')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 790,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                                            children: moduleAction === 'add' ? 'Adicionar Módulo' : 'Remover Módulo'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 794,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                            children: moduleAction === 'add' ? 'Esta ação afetará sua cobrança mensal' : 'Esta ação é irreversível'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 797,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 793,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 781,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: closeModuleConfirmModal,\n                                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 806,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 802,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 780,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-4 \".concat(moduleAction === 'add' ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800' : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    moduleAction === 'add' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-3 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 23\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 mr-3 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 822,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm \".concat(moduleAction === 'add' ? 'text-blue-800 dark:text-blue-200' : 'text-red-800 dark:text-red-200'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold mb-2\",\n                                                                children: moduleAction === 'add' ? '💰 ATENÇÃO: Impacto Financeiro' : '⚠️ ATENÇÃO: Consequências da Remoção'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 829,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"space-y-1 list-disc list-inside\",\n                                                                children: moduleAction === 'add' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"O valor ser\\xe1 adicionado \\xe0 sua mensalidade imediatamente\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 835,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"A cobran\\xe7a ser\\xe1 proporcional ao per\\xedodo restante do ciclo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 836,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Voc\\xea ter\\xe1 acesso completo ao m\\xf3dulo ap\\xf3s a confirma\\xe7\\xe3o\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 837,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"O m\\xf3dulo ficar\\xe1 ativo at\\xe9 o cancelamento manual\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 838,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Voc\\xea perder\\xe1 acesso a TODAS as funcionalidades do m\\xf3dulo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 842,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Os dados permanecer\\xe3o salvos, mas inacess\\xedveis\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 843,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Sua equipe n\\xe3o conseguir\\xe1 mais usar este m\\xf3dulo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 844,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Para reativar, ser\\xe1 necess\\xe1rio adicionar novamente\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 845,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 832,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 824,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 818,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 813,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 856,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        moduleAction === 'add' ? 'Módulo a ser adicionado:' : 'Módulo a ser removido:'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 855,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Nome:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 861,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: getModuleName(selectedModule.type)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 862,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 860,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Valor mensal:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 865,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        moduleAction === 'add' ? ((_selectedModule_info = selectedModule.info) === null || _selectedModule_info === void 0 ? void 0 : (_selectedModule_info_monthlyPrice = _selectedModule_info.monthlyPrice) === null || _selectedModule_info_monthlyPrice === void 0 ? void 0 : _selectedModule_info_monthlyPrice.toFixed(2)) || '0.00' : ((_selectedModule_info1 = selectedModule.info) === null || _selectedModule_info1 === void 0 ? void 0 : (_selectedModule_info_pricePerMonth = _selectedModule_info1.pricePerMonth) === null || _selectedModule_info_pricePerMonth === void 0 ? void 0 : _selectedModule_info_pricePerMonth.toFixed(2)) || '0.00'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 866,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 864,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        moduleAction === 'add' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Novo total mensal:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 875,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-blue-600 dark:text-blue-400\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        (planData.subscription.pricePerMonth + (((_selectedModule_info2 = selectedModule.info) === null || _selectedModule_info2 === void 0 ? void 0 : _selectedModule_info2.monthlyPrice) || 0)).toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 876,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 874,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        moduleAction === 'remove' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Novo total mensal:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 883,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-green-600 dark:text-green-400\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        (planData.subscription.pricePerMonth - (((_selectedModule_info3 = selectedModule.info) === null || _selectedModule_info3 === void 0 ? void 0 : _selectedModule_info3.pricePerMonth) || 0)).toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 884,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 882,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 859,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 854,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 895,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-yellow-800 dark:text-yellow-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium mb-1\",\n                                                                children: \"Confirma\\xe7\\xe3o necess\\xe1ria\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 897,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: moduleAction === 'add' ? 'Tem certeza de que deseja adicionar este módulo? O valor será cobrado imediatamente.' : 'Tem certeza de que deseja remover este módulo? Esta ação não pode ser desfeita facilmente.'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 898,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 896,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 894,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 893,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 811,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: closeModuleConfirmModal,\n                                            disabled: isUpdating,\n                                            className: \"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors disabled:opacity-50\",\n                                            children: \"Cancelar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 911,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: confirmModuleAction,\n                                            disabled: isUpdating,\n                                            className: \"px-4 py-2 text-white rounded-md transition-colors disabled:opacity-50 flex items-center \".concat(moduleAction === 'add' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-red-600 hover:bg-red-700'),\n                                            children: isUpdating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"animate-spin h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Processando...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    moduleAction === 'add' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 935,\n                                                        columnNumber: 25\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 937,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    moduleAction === 'add' ? 'Confirmar Adição' : 'Confirmar Remoção'\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 918,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 910,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 778,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 777,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 773,\n                columnNumber: 9\n            }, undefined),\n            showCancelModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50\",\n                        onClick: handleCloseCancelModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 953,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full mx-4 z-[11050]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-6 w-6 text-red-600 dark:text-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 961,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 960,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                                            children: \"Cancelar Assinatura\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 964,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                            children: \"Esta a\\xe7\\xe3o \\xe9 irrevers\\xedvel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 967,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 963,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 959,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCloseCancelModal,\n                                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 976,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 972,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 958,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 mr-3 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 985,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-red-800 dark:text-red-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold mb-2\",\n                                                                children: \"⚠️ ATEN\\xc7\\xc3O: Consequ\\xeancias do Cancelamento\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 987,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"space-y-1 list-disc list-inside\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Voc\\xea perder\\xe1 acesso a TODOS os m\\xf3dulos do sistema\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                        lineNumber: 989,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Todos os dados permanecer\\xe3o salvos, mas inacess\\xedveis\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                        lineNumber: 990,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Sua equipe n\\xe3o conseguir\\xe1 mais fazer login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                        lineNumber: 991,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Relat\\xf3rios e funcionalidades ficar\\xe3o bloqueados\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                        lineNumber: 992,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Para reativar, ser\\xe1 necess\\xe1rio contratar novamente\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                        lineNumber: 993,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 988,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 986,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 984,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 983,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1002,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"O que voc\\xea est\\xe1 cancelando:\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1001,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Empresa:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1007,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: planData.company.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1008,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1006,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Valor mensal:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1011,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        planData.subscription.pricePerMonth.toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1012,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1010,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Usu\\xe1rios ativos:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1015,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: planData.usage.currentUsers\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1016,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1014,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"M\\xf3dulos ativos:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1019,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: planData.modules.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1020,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1018,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Pr\\xf3xima cobran\\xe7a:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1023,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: planData.subscription.nextBillingDate ? new Date(planData.subscription.nextBillingDate).toLocaleDateString('pt-BR') : 'N/A'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1024,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1022,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1005,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1000,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: [\n                                                        \"Para confirmar o cancelamento, digite \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold text-red-600 dark:text-red-400\",\n                                                            children: '\"CANCELAR ASSINATURA\"'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1037,\n                                                            columnNumber: 59\n                                                        }, undefined),\n                                                        \" no campo abaixo:\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1036,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: cancelConfirmationText,\n                                                    onChange: (e)=>setCancelConfirmationText(e.target.value),\n                                                    placeholder: \"Digite: CANCELAR ASSINATURA\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-red-500 focus:border-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1039,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1035,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1051,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-yellow-800 dark:text-yellow-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium mb-1\",\n                                                                children: \"\\xdaltima chance!\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1053,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Tem certeza de que deseja cancelar? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita e voc\\xea precisar\\xe1 contratar novamente para ter acesso ao sistema.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1054,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1052,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1050,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1049,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 981,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCloseCancelModal,\n                                            disabled: isUpdating,\n                                            className: \"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors disabled:opacity-50\",\n                                            children: \"Manter Assinatura\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1062,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCancelSubscription,\n                                            disabled: isUpdating || cancelConfirmationText !== 'CANCELAR ASSINATURA',\n                                            className: \"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors disabled:opacity-50 flex items-center\",\n                                            children: isUpdating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"animate-spin h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1076,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Cancelando...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1081,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Confirmar Cancelamento\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1069,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1061,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 956,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 955,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 951,\n                columnNumber: 9\n            }, undefined),\n            showAddUsersModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50\",\n                        onClick: handleCloseAddUsersModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 1096,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 z-[11050]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5 text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1103,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Adicionar Usu\\xe1rios ao Plano\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1102,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCloseAddUsersModal,\n                                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1110,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1106,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1101,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-gray-100 mb-2\",\n                                                    children: \"Plano Atual\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1118,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Usu\\xe1rios atuais: \",\n                                                                planData.usage.currentUsers,\n                                                                \" / \",\n                                                                planData.usage.userLimit\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1120,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Pre\\xe7o atual: R$ \",\n                                                                planData.subscription.pricePerMonth.toFixed(2),\n                                                                \"/m\\xeas\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1121,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Pre\\xe7o por usu\\xe1rio: R$ \",\n                                                                calculatePricePerUser().toFixed(2),\n                                                                \"/m\\xeas\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1122,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1119,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1117,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: \"Quantidade de usu\\xe1rios a adicionar\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1128,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setAdditionalUsersCount(Math.max(1, additionalUsersCount - 1)),\n                                                            className: \"flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1136,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1132,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            min: \"1\",\n                                                            max: \"100\",\n                                                            value: additionalUsersCount,\n                                                            onChange: (e)=>setAdditionalUsersCount(Math.max(1, parseInt(e.target.value) || 1)),\n                                                            className: \"w-20 text-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1138,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setAdditionalUsersCount(Math.min(100, additionalUsersCount + 1)),\n                                                            className: \"flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1150,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1146,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1131,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1127,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-blue-900 dark:text-blue-100 mb-2\",\n                                                    children: \"Resumo da Altera\\xe7\\xe3o\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1157,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-blue-800 dark:text-blue-200 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Usu\\xe1rios adicionais: \",\n                                                                additionalUsersCount\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1159,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Custo adicional: R$ \",\n                                                                calculateAdditionalCost().toFixed(2),\n                                                                \"/m\\xeas\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1160,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-semibold\",\n                                                            children: [\n                                                                \"Novo total: R$ \",\n                                                                (planData.subscription.pricePerMonth + calculateAdditionalCost()).toFixed(2),\n                                                                \"/m\\xeas\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1161,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-blue-600 dark:text-blue-300 mt-2\",\n                                                            children: \"* A cobran\\xe7a ser\\xe1 proporcional ao per\\xedodo restante do ciclo atual\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1164,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1158,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1156,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1173,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-yellow-800 dark:text-yellow-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium mb-1\",\n                                                                children: \"Aten\\xe7\\xe3o:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1175,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Esta a\\xe7\\xe3o ir\\xe1 aumentar o valor da sua assinatura mensalmente. A cobran\\xe7a adicional ser\\xe1 aplicada imediatamente de forma proporcional.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1176,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1174,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1172,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1171,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1115,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCloseAddUsersModal,\n                                            disabled: isUpdating,\n                                            className: \"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors disabled:opacity-50\",\n                                            children: \"Cancelar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1184,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleAddUsers,\n                                            disabled: isUpdating,\n                                            className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 flex items-center\",\n                                            children: isUpdating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"animate-spin h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1198,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Processando...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1203,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Confirmar Adi\\xe7\\xe3o\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1191,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1183,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1099,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 1098,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 1094,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n        lineNumber: 529,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlansPage, \"uyVNLCmUUDe7gZx8rYsg7PmvV28=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = PlansPage;\n// Função auxiliar para obter nome do módulo\nconst getModuleName = (moduleType)=>{\n    const moduleNames = {\n        'BASIC': 'Módulo Básico',\n        'ADMIN': 'Administração',\n        'SCHEDULING': 'Agendamento',\n        'PEOPLE': 'Pessoas',\n        'REPORTS': 'Relatórios',\n        'CHAT': 'Chat',\n        'ABAPLUS': 'ABA+'\n    };\n    return moduleNames[moduleType] || moduleType;\n};\n// Função auxiliar para verificar se é módulo básico\nconst isBasicModule = (moduleType)=>{\n    return [\n        'BASIC',\n        'ADMIN',\n        'SCHEDULING'\n    ].includes(moduleType);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlansPage);\nvar _c;\n$RefreshReg$(_c, \"PlansPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js\n"));

/***/ })

});