"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/plans/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js":
/*!**************************************************!*\
  !*** ./src/app/modules/admin/plans/PlansPage.js ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_pricing__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/pricing */ \"(app-pages-browser)/./src/utils/pricing.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ban.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/modules/admin/services/plansService */ \"(app-pages-browser)/./src/app/modules/admin/services/plansService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst PlansPage = ()=>{\n    var _selectedModule_info_monthlyPrice, _selectedModule_info, _selectedModule_info_pricePerMonth, _selectedModule_info1, _selectedModule_info2, _selectedModule_info3;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { can } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions)();\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const [planData, setPlanData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [availablePlans, setAvailablePlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCompanyId, setSelectedCompanyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para o modal de adicionar usuários\n    const [showAddUsersModal, setShowAddUsersModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [additionalUsersCount, setAdditionalUsersCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cancelConfirmationText, setCancelConfirmationText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Estados para o modal de confirmação de módulos\n    const [showModuleConfirmModal, setShowModuleConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [moduleAction, setModuleAction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // 'add' ou 'remove'\n    const [selectedModule, setSelectedModule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Verificar se o usuário atual é um system_admin\n    const isSystemAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    // Função para carregar empresas (apenas para system_admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const response = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_8__.companyService.getCompaniesForSelect();\n            setCompanies(response);\n            // Se não há empresa selecionada e há empresas disponíveis, selecionar a primeira\n            if (!selectedCompanyId && response.length > 0) {\n                setSelectedCompanyId(response[0].id);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível carregar as empresas.\"\n            });\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    // Função para carregar dados do plano\n    const loadPlanData = async function() {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        console.log('[DEBUG] ===== INICIANDO loadPlanData =====');\n        console.log('[DEBUG] forceRefresh:', forceRefresh);\n        console.log('[DEBUG] isSystemAdmin:', isSystemAdmin);\n        console.log('[DEBUG] selectedCompanyId:', selectedCompanyId);\n        // Para system_admin, não carregar se não tiver empresa selecionada\n        if (isSystemAdmin && !selectedCompanyId) {\n            console.log('[DEBUG] System admin sem empresa selecionada, não carregando dados');\n            setIsLoading(false);\n            return;\n        }\n        setIsLoading(true);\n        try {\n            var _planResponse_modules, _planResponse_modules1, _planData_modules;\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            console.log('[DEBUG] Carregando dados do plano para empresa:', companyId, 'forceRefresh:', forceRefresh);\n            console.log('[DEBUG] Fazendo chamadas para API...');\n            const [planResponse, availablePlansResponse] = await Promise.all([\n                _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.getPlansData(companyId, forceRefresh),\n                _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.getAvailablePlans()\n            ]);\n            console.log('[DEBUG] ===== RESPOSTA RECEBIDA =====');\n            console.log('[DEBUG] planResponse completo:', JSON.stringify(planResponse, null, 2));\n            console.log('[DEBUG] availablePlansResponse completo:', JSON.stringify(availablePlansResponse, null, 2));\n            console.log('[DEBUG] Módulos ativos:', planResponse === null || planResponse === void 0 ? void 0 : (_planResponse_modules = planResponse.modules) === null || _planResponse_modules === void 0 ? void 0 : _planResponse_modules.map((m)=>\"\".concat(m.moduleType, \" (\").concat(m.active ? 'ATIVO' : 'INATIVO', \")\")));\n            console.log('[DEBUG] Quantidade de módulos:', planResponse === null || planResponse === void 0 ? void 0 : (_planResponse_modules1 = planResponse.modules) === null || _planResponse_modules1 === void 0 ? void 0 : _planResponse_modules1.length);\n            console.log('[DEBUG] availablePlans.modules:', availablePlansResponse === null || availablePlansResponse === void 0 ? void 0 : availablePlansResponse.modules);\n            console.log('[DEBUG] ===== ATUALIZANDO ESTADO =====');\n            console.log('[DEBUG] Estado anterior planData:', planData === null || planData === void 0 ? void 0 : (_planData_modules = planData.modules) === null || _planData_modules === void 0 ? void 0 : _planData_modules.map((m)=>m.moduleType));\n            setPlanData(planResponse);\n            setAvailablePlans(availablePlansResponse);\n            console.log('[DEBUG] ===== ESTADO ATUALIZADO =====');\n        } catch (error) {\n            var _error_response;\n            console.error(\"[DEBUG] ===== ERRO AO CARREGAR DADOS =====\");\n            console.error(\"Erro ao carregar dados do plano:\", error);\n            console.error(\"Error details:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível carregar os dados do plano.\"\n            });\n        } finally{\n            setIsLoading(false);\n            console.log('[DEBUG] ===== FIM loadPlanData =====');\n        }\n    };\n    // Carregar dados iniciais\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (isSystemAdmin) {\n                loadCompanies();\n            } else {\n                loadPlanData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        isSystemAdmin\n    ]);\n    // Recarregar dados quando a empresa selecionada mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (isSystemAdmin && selectedCompanyId) {\n                loadPlanData();\n            } else if (!isSystemAdmin) {\n                loadPlanData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        selectedCompanyId,\n        isSystemAdmin\n    ]);\n    // Monitor planData changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            var _planData_modules;\n            console.log('[DEBUG] ===== PLANDATA MUDOU =====');\n            console.log('[DEBUG] planData:', planData);\n            console.log('[DEBUG] Módulos no estado:', planData === null || planData === void 0 ? void 0 : (_planData_modules = planData.modules) === null || _planData_modules === void 0 ? void 0 : _planData_modules.map({\n                \"PlansPage.useEffect\": (m)=>\"\".concat(m.moduleType, \" (\").concat(m.active ? 'ATIVO' : 'INATIVO', \")\")\n            }[\"PlansPage.useEffect\"]));\n            console.log('[DEBUG] ================================');\n        }\n    }[\"PlansPage.useEffect\"], [\n        planData\n    ]);\n    // Função para abrir modal de adicionar usuários\n    const handleOpenAddUsersModal = ()=>{\n        setAdditionalUsersCount(1);\n        setShowAddUsersModal(true);\n    };\n    // Função para fechar modal de adicionar usuários\n    const handleCloseAddUsersModal = ()=>{\n        setShowAddUsersModal(false);\n        setAdditionalUsersCount(1);\n    };\n    // Função para abrir modal de cancelamento\n    const handleOpenCancelModal = ()=>{\n        setCancelConfirmationText('');\n        setShowCancelModal(true);\n    };\n    // Função para fechar modal de cancelamento\n    const handleCloseCancelModal = ()=>{\n        setShowCancelModal(false);\n        setCancelConfirmationText('');\n    };\n    // Função para calcular o custo adicional usando a função centralizada\n    const calculateAdditionalCost = ()=>{\n        if (!planData) return {\n            additionalCost: 0,\n            costPerAdditionalUser: 19.90\n        };\n        const currentUsers = planData.subscription.userLimit;\n        const isAnnual = planData.subscription.billingCycle === 'YEARLY';\n        return (0,_utils_pricing__WEBPACK_IMPORTED_MODULE_3__.calculateAdditionalUsersCost)(currentUsers, additionalUsersCount, isAnnual);\n    };\n    // Função para adicionar usuários (confirmada pelo modal)\n    const handleAddUsers = async ()=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.addUsers(additionalUsersCount, companyId);\n            const additionalCost = calculateAdditionalCost();\n            toast_success({\n                title: \"Usuários Adicionados\",\n                message: \"\".concat(additionalUsersCount, \" usu\\xe1rio(s) adicionado(s) ao plano. Custo adicional: R$ \").concat(additionalCost.toFixed(2), \"/m\\xeas.\")\n            });\n            handleCloseAddUsersModal();\n            loadPlanData(true); // Force refresh\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Erro ao adicionar usuários:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Não foi possível adicionar usuários ao plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para abrir modal de confirmação para adicionar módulo\n    const openAddModuleConfirmation = (moduleType)=>{\n        console.log('[DEBUG] openAddModuleConfirmation:', {\n            moduleType,\n            availablePlans\n        });\n        console.log('[DEBUG] availablePlans.modules:', availablePlans === null || availablePlans === void 0 ? void 0 : availablePlans.modules);\n        if (!(availablePlans === null || availablePlans === void 0 ? void 0 : availablePlans.modules)) {\n            console.error('[DEBUG] availablePlans.modules não está disponível');\n            toast_error({\n                title: \"Erro\",\n                message: \"Dados dos módulos não estão disponíveis. Tente recarregar a página.\"\n            });\n            return;\n        }\n        const moduleInfo = availablePlans.modules[moduleType];\n        console.log('[DEBUG] moduleInfo encontrado:', moduleInfo);\n        setSelectedModule({\n            type: moduleType,\n            info: moduleInfo\n        });\n        setModuleAction('add');\n        setShowModuleConfirmModal(true);\n    };\n    // Função para abrir modal de confirmação para remover módulo\n    const openRemoveModuleConfirmation = (moduleType)=>{\n        var _planData_modules;\n        const moduleInfo = planData === null || planData === void 0 ? void 0 : (_planData_modules = planData.modules) === null || _planData_modules === void 0 ? void 0 : _planData_modules.find((m)=>m.moduleType === moduleType);\n        setSelectedModule({\n            type: moduleType,\n            info: moduleInfo\n        });\n        setModuleAction('remove');\n        setShowModuleConfirmModal(true);\n    };\n    // Função para fechar modal de confirmação de módulos\n    const closeModuleConfirmModal = ()=>{\n        setShowModuleConfirmModal(false);\n        setModuleAction(null);\n        setSelectedModule(null);\n    };\n    // Função para confirmar a ação do módulo\n    const confirmModuleAction = async ()=>{\n        if (!selectedModule || !moduleAction) return;\n        closeModuleConfirmModal();\n        if (moduleAction === 'add') {\n            await handleAddModule(selectedModule.type);\n        } else if (moduleAction === 'remove') {\n            await handleRemoveModule(selectedModule.type);\n        }\n    };\n    // Função para adicionar módulo\n    const handleAddModule = async (moduleType)=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.addModule(moduleType, companyId);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Módulo adicionado ao plano com sucesso.\"\n            });\n            // Aguardar um pouco para garantir que o cache foi invalidado\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            await loadPlanData(true); // Force refresh\n        } catch (error) {\n            console.error(\"Erro ao adicionar módulo:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível adicionar o módulo ao plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para remover módulo\n    const handleRemoveModule = async (moduleType)=>{\n        console.log('[DEBUG] Iniciando remoção do módulo:', moduleType);\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            console.log('[DEBUG] Removendo módulo para empresa:', companyId);\n            const result = await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.removeModule(moduleType, companyId);\n            console.log('[DEBUG] Resultado da remoção:', result);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Módulo removido do plano com sucesso.\"\n            });\n            console.log('[DEBUG] Aguardando invalidação de cache...');\n            // Aguardar um pouco para garantir que o cache foi invalidado\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            console.log('[DEBUG] Recarregando dados do plano...');\n            await loadPlanData(true); // Force refresh para evitar cache\n            console.log('[DEBUG] Dados recarregados');\n        } catch (error) {\n            console.error(\"Erro ao remover módulo:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível remover o módulo do plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para cancelar assinatura (confirmada pelo modal)\n    const handleCancelSubscription = async ()=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.cancelSubscription(companyId);\n            toast_success({\n                title: \"Assinatura Cancelada\",\n                message: \"Sua assinatura foi cancelada com sucesso. O acesso será mantido até o final do período pago.\"\n            });\n            handleCloseCancelModal();\n            loadPlanData(true); // Force refresh\n        } catch (error) {\n            console.error(\"Erro ao cancelar assinatura:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível cancelar a assinatura.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para reativar assinatura\n    const handleReactivateSubscription = async ()=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.reactivateSubscription(companyId);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Assinatura reativada com sucesso.\"\n            });\n            loadPlanData();\n        } catch (error) {\n            console.error(\"Erro ao reativar assinatura:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível reativar a assinatura.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para formatar status\n    const getStatusInfo = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return {\n                    label: 'Ativo',\n                    color: 'text-green-600 dark:text-green-400',\n                    bgColor: 'bg-green-100 dark:bg-green-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                };\n            case 'CANCELED':\n                return {\n                    label: 'Cancelado',\n                    color: 'text-red-600 dark:text-red-400',\n                    bgColor: 'bg-red-100 dark:bg-red-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                };\n            case 'PAST_DUE':\n                return {\n                    label: 'Em Atraso',\n                    color: 'text-yellow-600 dark:text-yellow-400',\n                    bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                };\n            default:\n                return {\n                    label: status,\n                    color: 'text-gray-600 dark:text-gray-400',\n                    bgColor: 'bg-gray-100 dark:bg-gray-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                };\n        }\n    };\n    // Função para formatar ciclo de cobrança\n    const getBillingCycleLabel = (cycle)=>{\n        switch(cycle){\n            case 'MONTHLY':\n                return 'Mensal';\n            case 'YEARLY':\n                return 'Anual';\n            default:\n                return cycle;\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"animate-spin h-8 w-8 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 431,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2 text-gray-600 dark:text-gray-400\",\n                    children: \"Carregando dados do plano...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 432,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 430,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Mostrar mensagem para system_admin quando nenhuma empresa está selecionada\n    if (isSystemAdmin && !selectedCompanyId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        size: 22,\n                        className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 443,\n                        columnNumber: 17\n                    }, void 0),\n                    description: \"Gerencie planos, usu\\xe1rios e m\\xf3dulos das assinaturas das empresas.\",\n                    moduleColor: \"admin\",\n                    filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full sm:w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleSelect, {\n                            moduleColor: \"admin\",\n                            value: selectedCompanyId,\n                            onChange: (e)=>setSelectedCompanyId(e.target.value),\n                            placeholder: \"Selecione uma empresa\",\n                            disabled: isLoadingCompanies,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Selecione uma empresa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 455,\n                                    columnNumber: 17\n                                }, void 0),\n                                companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: company.id,\n                                        children: company.name\n                                    }, company.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 457,\n                                        columnNumber: 19\n                                    }, void 0))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 448,\n                            columnNumber: 15\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 447,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 441,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 467,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            children: \"Selecione uma empresa\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 468,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"Escolha uma empresa no seletor acima para visualizar e gerenciar seu plano.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 471,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 466,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 440,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!planData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        size: 22,\n                        className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 484,\n                        columnNumber: 17\n                    }, void 0),\n                    description: \"Gerencie seu plano, usu\\xe1rios e m\\xf3dulos da assinatura.\",\n                    moduleColor: \"admin\",\n                    filters: isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full sm:w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleSelect, {\n                            moduleColor: \"admin\",\n                            value: selectedCompanyId,\n                            onChange: (e)=>setSelectedCompanyId(e.target.value),\n                            placeholder: \"Selecione uma empresa\",\n                            disabled: isLoadingCompanies,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Selecione uma empresa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 497,\n                                    columnNumber: 19\n                                }, void 0),\n                                companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: company.id,\n                                        children: company.name\n                                    }, company.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 499,\n                                        columnNumber: 21\n                                    }, void 0))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 490,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 489,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 482,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 510,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            children: \"Nenhum plano encontrado\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 511,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"N\\xe3o foi poss\\xedvel encontrar informa\\xe7\\xf5es do plano para esta empresa.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 514,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 509,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 481,\n            columnNumber: 7\n        }, undefined);\n    }\n    const statusInfo = getStatusInfo(planData.subscription.status);\n    const StatusIcon = statusInfo.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleHeader, {\n                title: \"Gerenciamento de Planos\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 530,\n                    columnNumber: 15\n                }, void 0),\n                description: isSystemAdmin ? \"Gerencie o plano, usu\\xe1rios e m\\xf3dulos da assinatura de \".concat(planData.company.name, \".\") : \"Gerencie seu plano, usuários e módulos da assinatura.\",\n                moduleColor: \"admin\",\n                filters: isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full sm:w-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleSelect, {\n                        moduleColor: \"admin\",\n                        value: selectedCompanyId,\n                        onChange: (e)=>setSelectedCompanyId(e.target.value),\n                        placeholder: \"Selecione uma empresa\",\n                        disabled: isLoadingCompanies,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"Selecione uma empresa\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 546,\n                                columnNumber: 17\n                            }, void 0),\n                            companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: company.id,\n                                    children: company.name\n                                }, company.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 548,\n                                    columnNumber: 19\n                                }, void 0))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 539,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 538,\n                    columnNumber: 13\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 528,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 text-yellow-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 564,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Plano Atual\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 563,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(statusInfo.bgColor, \" \").concat(statusInfo.color),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 568,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            statusInfo.label\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 567,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 562,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Empresa\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: planData.company.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 575,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Ciclo de Cobran\\xe7a\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: getBillingCycleLabel(planData.subscription.billingCycle)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 581,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 574,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pre\\xe7o Mensal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-gray-900 dark:text-gray-100\",\n                                                        children: [\n                                                            \"R$ \",\n                                                            planData.subscription.pricePerMonth.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 590,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pr\\xf3xima Cobran\\xe7a\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: planData.subscription.nextBillingDate ? new Date(planData.subscription.nextBillingDate).toLocaleDateString('pt-BR') : 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 596,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 589,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 pt-4 border-t border-gray-200 dark:border-gray-700\",\n                                        children: [\n                                            planData.subscription.status === 'ACTIVE' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleOpenCancelModal,\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Cancelar Plano\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 610,\n                                                columnNumber: 17\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleReactivateSubscription,\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Reativar Plano\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 619,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    const url = isSystemAdmin && selectedCompanyId ? \"/subscription/invoices?companyId=\".concat(selectedCompanyId) : '/subscription/invoices';\n                                                    router.push(url);\n                                                },\n                                                className: \"flex items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Ver Faturas\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 629,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 608,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 573,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 561,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 648,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Usu\\xe1rios\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 647,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Uso atual\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            planData.usage.currentUsers,\n                                                            \" / \",\n                                                            planData.usage.userLimit\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 654,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\",\n                                                    style: {\n                                                        width: \"\".concat(Math.min(planData.usage.userLimitUsage, 100), \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 659,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 658,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                                children: [\n                                                    planData.usage.userLimitUsage,\n                                                    \"% utilizado\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 664,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 653,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                                children: [\n                                                    planData.usage.availableUsers,\n                                                    \" usu\\xe1rios dispon\\xedveis\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 670,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleOpenAddUsersModal,\n                                                disabled: isUpdating,\n                                                className: \"w-full flex items-center justify-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Adicionar Usu\\xe1rios\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 673,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 669,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 652,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 646,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 559,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"mr-2 h-5 w-5 text-purple-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 689,\n                                columnNumber: 11\n                            }, undefined),\n                            \"M\\xf3dulos da Assinatura\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 688,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: [\n                            planData.modules.map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-green-200 dark:border-green-800 rounded-lg p-4 bg-green-50 dark:bg-green-900/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-500 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                            children: getModuleName(module.moduleType)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-green-600 dark:text-green-400 font-medium\",\n                                                    children: \"Ativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 697,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: [\n                                                \"R$ \",\n                                                module.pricePerMonth.toFixed(2),\n                                                \"/m\\xeas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 708,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                            children: [\n                                                \"Adicionado em \",\n                                                new Date(module.addedAt).toLocaleDateString('pt-BR')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 711,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        !isBasicModule(module.moduleType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>openRemoveModuleConfirmation(module.moduleType),\n                                            disabled: isUpdating,\n                                            className: \"mt-3 w-full flex items-center justify-center px-2 py-1 bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:hover:bg-red-900/50 text-red-700 dark:text-red-400 text-xs font-medium rounded transition-colors disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"mr-1 h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 722,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Remover\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 717,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, module.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 696,\n                                    columnNumber: 13\n                                }, undefined)),\n                            availablePlans && Object.entries(availablePlans.modules).filter((param)=>{\n                                let [moduleType, moduleInfo] = param;\n                                return !planData.modules.some((m)=>m.moduleType === moduleType) && !moduleInfo.included;\n                            }).map((param)=>{\n                                let [moduleType, moduleInfo] = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-900/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 739,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                            children: moduleInfo.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 740,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400 font-medium\",\n                                                    children: \"Dispon\\xedvel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 744,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 737,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: moduleInfo.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 748,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-3\",\n                                            children: [\n                                                \"R$ \",\n                                                moduleInfo.monthlyPrice.toFixed(2),\n                                                \"/m\\xeas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 751,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>openAddModuleConfirmation(moduleType),\n                                            disabled: isUpdating,\n                                            className: \"w-full flex items-center justify-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded transition-colors disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"mr-1 h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 760,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Adicionar\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 755,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, moduleType, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 736,\n                                    columnNumber: 15\n                                }, undefined);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 693,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 687,\n                columnNumber: 7\n            }, undefined),\n            showModuleConfirmModal && selectedModule && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50\",\n                        onClick: closeModuleConfirmModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 772,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full mx-4 z-[11050]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center w-12 h-12 rounded-full mr-4 \".concat(moduleAction === 'add' ? 'bg-blue-100 dark:bg-blue-900/30' : 'bg-red-100 dark:bg-red-900/30'),\n                                                    children: moduleAction === 'add' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 \".concat(moduleAction === 'add' ? 'text-blue-600 dark:text-blue-400' : 'text-red-600 dark:text-red-400')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 785,\n                                                        columnNumber: 23\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-6 w-6 \".concat(moduleAction === 'add' ? 'text-blue-600 dark:text-blue-400' : 'text-red-600 dark:text-red-400')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 787,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                                            children: moduleAction === 'add' ? 'Adicionar Módulo' : 'Remover Módulo'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 791,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                            children: moduleAction === 'add' ? 'Esta ação afetará sua cobrança mensal' : 'Esta ação é irreversível'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 794,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 790,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 778,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: closeModuleConfirmModal,\n                                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 803,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 799,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 777,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-4 \".concat(moduleAction === 'add' ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800' : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    moduleAction === 'add' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-3 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 817,\n                                                        columnNumber: 23\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 mr-3 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 819,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm \".concat(moduleAction === 'add' ? 'text-blue-800 dark:text-blue-200' : 'text-red-800 dark:text-red-200'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold mb-2\",\n                                                                children: moduleAction === 'add' ? '💰 ATENÇÃO: Impacto Financeiro' : '⚠️ ATENÇÃO: Consequências da Remoção'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 826,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"space-y-1 list-disc list-inside\",\n                                                                children: moduleAction === 'add' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"O valor ser\\xe1 adicionado \\xe0 sua mensalidade imediatamente\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 832,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"A cobran\\xe7a ser\\xe1 proporcional ao per\\xedodo restante do ciclo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 833,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Voc\\xea ter\\xe1 acesso completo ao m\\xf3dulo ap\\xf3s a confirma\\xe7\\xe3o\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 834,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"O m\\xf3dulo ficar\\xe1 ativo at\\xe9 o cancelamento manual\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 835,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Voc\\xea perder\\xe1 acesso a TODAS as funcionalidades do m\\xf3dulo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 839,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Os dados permanecer\\xe3o salvos, mas inacess\\xedveis\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 840,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Sua equipe n\\xe3o conseguir\\xe1 mais usar este m\\xf3dulo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 841,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Para reativar, ser\\xe1 necess\\xe1rio adicionar novamente\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 842,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 829,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 821,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 815,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 810,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 853,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        moduleAction === 'add' ? 'Módulo a ser adicionado:' : 'Módulo a ser removido:'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 852,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Nome:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 858,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: getModuleName(selectedModule.type)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 859,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 857,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Valor mensal:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 862,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        moduleAction === 'add' ? ((_selectedModule_info = selectedModule.info) === null || _selectedModule_info === void 0 ? void 0 : (_selectedModule_info_monthlyPrice = _selectedModule_info.monthlyPrice) === null || _selectedModule_info_monthlyPrice === void 0 ? void 0 : _selectedModule_info_monthlyPrice.toFixed(2)) || '0.00' : ((_selectedModule_info1 = selectedModule.info) === null || _selectedModule_info1 === void 0 ? void 0 : (_selectedModule_info_pricePerMonth = _selectedModule_info1.pricePerMonth) === null || _selectedModule_info_pricePerMonth === void 0 ? void 0 : _selectedModule_info_pricePerMonth.toFixed(2)) || '0.00'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 863,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 861,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        moduleAction === 'add' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Novo total mensal:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 872,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-blue-600 dark:text-blue-400\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        (planData.subscription.pricePerMonth + (((_selectedModule_info2 = selectedModule.info) === null || _selectedModule_info2 === void 0 ? void 0 : _selectedModule_info2.monthlyPrice) || 0)).toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 873,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 871,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        moduleAction === 'remove' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Novo total mensal:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 880,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-green-600 dark:text-green-400\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        (planData.subscription.pricePerMonth - (((_selectedModule_info3 = selectedModule.info) === null || _selectedModule_info3 === void 0 ? void 0 : _selectedModule_info3.pricePerMonth) || 0)).toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 881,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 879,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 856,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 851,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 892,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-yellow-800 dark:text-yellow-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium mb-1\",\n                                                                children: \"Confirma\\xe7\\xe3o necess\\xe1ria\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 894,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: moduleAction === 'add' ? 'Tem certeza de que deseja adicionar este módulo? O valor será cobrado imediatamente.' : 'Tem certeza de que deseja remover este módulo? Esta ação não pode ser desfeita facilmente.'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 895,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 893,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 891,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 890,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 808,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: closeModuleConfirmModal,\n                                            disabled: isUpdating,\n                                            className: \"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors disabled:opacity-50\",\n                                            children: \"Cancelar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 908,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: confirmModuleAction,\n                                            disabled: isUpdating,\n                                            className: \"px-4 py-2 text-white rounded-md transition-colors disabled:opacity-50 flex items-center \".concat(moduleAction === 'add' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-red-600 hover:bg-red-700'),\n                                            children: isUpdating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"animate-spin h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 926,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Processando...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    moduleAction === 'add' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 932,\n                                                        columnNumber: 25\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 934,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    moduleAction === 'add' ? 'Confirmar Adição' : 'Confirmar Remoção'\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 915,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 907,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 775,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 774,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 770,\n                columnNumber: 9\n            }, undefined),\n            showCancelModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50\",\n                        onClick: handleCloseCancelModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 950,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full mx-4 z-[11050]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-6 w-6 text-red-600 dark:text-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 958,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 957,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                                            children: \"Cancelar Assinatura\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 961,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                            children: \"Esta a\\xe7\\xe3o \\xe9 irrevers\\xedvel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 964,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 960,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 956,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCloseCancelModal,\n                                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 973,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 969,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 955,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 mr-3 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 982,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-red-800 dark:text-red-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold mb-2\",\n                                                                children: \"⚠️ ATEN\\xc7\\xc3O: Consequ\\xeancias do Cancelamento\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 984,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"space-y-1 list-disc list-inside\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Voc\\xea perder\\xe1 acesso a TODOS os m\\xf3dulos do sistema\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                        lineNumber: 986,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Todos os dados permanecer\\xe3o salvos, mas inacess\\xedveis\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                        lineNumber: 987,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Sua equipe n\\xe3o conseguir\\xe1 mais fazer login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                        lineNumber: 988,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Relat\\xf3rios e funcionalidades ficar\\xe3o bloqueados\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                        lineNumber: 989,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Para reativar, ser\\xe1 necess\\xe1rio contratar novamente\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                        lineNumber: 990,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 985,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 983,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 981,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 980,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 999,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"O que voc\\xea est\\xe1 cancelando:\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 998,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Empresa:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1004,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: planData.company.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1005,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1003,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Valor mensal:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1008,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        planData.subscription.pricePerMonth.toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1009,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1007,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Usu\\xe1rios ativos:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1012,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: planData.usage.currentUsers\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1013,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1011,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"M\\xf3dulos ativos:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1016,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: planData.modules.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1017,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1015,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Pr\\xf3xima cobran\\xe7a:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1020,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: planData.subscription.nextBillingDate ? new Date(planData.subscription.nextBillingDate).toLocaleDateString('pt-BR') : 'N/A'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1021,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1019,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1002,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 997,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: [\n                                                        \"Para confirmar o cancelamento, digite \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold text-red-600 dark:text-red-400\",\n                                                            children: '\"CANCELAR ASSINATURA\"'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1034,\n                                                            columnNumber: 59\n                                                        }, undefined),\n                                                        \" no campo abaixo:\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1033,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: cancelConfirmationText,\n                                                    onChange: (e)=>setCancelConfirmationText(e.target.value),\n                                                    placeholder: \"Digite: CANCELAR ASSINATURA\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-red-500 focus:border-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1036,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1032,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-yellow-800 dark:text-yellow-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium mb-1\",\n                                                                children: \"\\xdaltima chance!\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1050,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Tem certeza de que deseja cancelar? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita e voc\\xea precisar\\xe1 contratar novamente para ter acesso ao sistema.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1051,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1049,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1047,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1046,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 978,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCloseCancelModal,\n                                            disabled: isUpdating,\n                                            className: \"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors disabled:opacity-50\",\n                                            children: \"Manter Assinatura\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1059,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCancelSubscription,\n                                            disabled: isUpdating || cancelConfirmationText !== 'CANCELAR ASSINATURA',\n                                            className: \"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors disabled:opacity-50 flex items-center\",\n                                            children: isUpdating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"animate-spin h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1073,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Cancelando...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1078,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Confirmar Cancelamento\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1066,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1058,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 953,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 952,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 948,\n                columnNumber: 9\n            }, undefined),\n            showAddUsersModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50\",\n                        onClick: handleCloseAddUsersModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 1093,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 z-[11050]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5 text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1100,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Adicionar Usu\\xe1rios ao Plano\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1099,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCloseAddUsersModal,\n                                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1107,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1103,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1098,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-gray-100 mb-2\",\n                                                    children: \"Plano Atual\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1115,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Usu\\xe1rios atuais: \",\n                                                                planData.usage.currentUsers,\n                                                                \" / \",\n                                                                planData.usage.userLimit\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1117,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Pre\\xe7o atual: R$ \",\n                                                                planData.subscription.pricePerMonth.toFixed(2),\n                                                                \"/m\\xeas\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1118,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Pre\\xe7o por usu\\xe1rio: R$ \",\n                                                                calculatePricePerUser().toFixed(2),\n                                                                \"/m\\xeas\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1119,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1116,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1114,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: \"Quantidade de usu\\xe1rios a adicionar\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1125,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setAdditionalUsersCount(Math.max(1, additionalUsersCount - 1)),\n                                                            className: \"flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1133,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1129,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            min: \"1\",\n                                                            max: \"100\",\n                                                            value: additionalUsersCount,\n                                                            onChange: (e)=>setAdditionalUsersCount(Math.max(1, parseInt(e.target.value) || 1)),\n                                                            className: \"w-20 text-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1135,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setAdditionalUsersCount(Math.min(100, additionalUsersCount + 1)),\n                                                            className: \"flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1147,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1143,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1128,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1124,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-blue-900 dark:text-blue-100 mb-2\",\n                                                    children: \"Resumo da Altera\\xe7\\xe3o\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1154,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-blue-800 dark:text-blue-200 space-y-1\",\n                                                    children: (()=>{\n                                                        const costInfo = calculateAdditionalCost();\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Usu\\xe1rios adicionais: \",\n                                                                        additionalUsersCount\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1160,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Custo adicional: \",\n                                                                        (0,_utils_pricing__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(costInfo.additionalCost),\n                                                                        \"/m\\xeas\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1161,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: [\n                                                                        \"Novo total: \",\n                                                                        (0,_utils_pricing__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(costInfo.newPrice),\n                                                                        \"/m\\xeas\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1162,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                costInfo.discountImprovement > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-green-600 dark:text-green-400 text-xs\",\n                                                                    children: [\n                                                                        \"✨ Desconto melhorou de \",\n                                                                        costInfo.newDiscount - costInfo.discountImprovement,\n                                                                        \"% para \",\n                                                                        costInfo.newDiscount,\n                                                                        \"%!\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1166,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-blue-600 dark:text-blue-300 mt-2\",\n                                                                    children: \"* A cobran\\xe7a ser\\xe1 proporcional ao per\\xedodo restante do ciclo atual\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1170,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true);\n                                                    })()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1155,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1153,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1182,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-yellow-800 dark:text-yellow-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium mb-1\",\n                                                                children: \"Aten\\xe7\\xe3o:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1184,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Esta a\\xe7\\xe3o ir\\xe1 aumentar o valor da sua assinatura mensalmente. A cobran\\xe7a adicional ser\\xe1 aplicada imediatamente de forma proporcional.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1185,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1183,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1181,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1180,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1112,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCloseAddUsersModal,\n                                            disabled: isUpdating,\n                                            className: \"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors disabled:opacity-50\",\n                                            children: \"Cancelar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1193,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleAddUsers,\n                                            disabled: isUpdating,\n                                            className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 flex items-center\",\n                                            children: isUpdating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"animate-spin h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1207,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Processando...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1212,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Confirmar Adi\\xe7\\xe3o\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1200,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1192,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1096,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 1095,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 1091,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n        lineNumber: 526,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlansPage, \"uyVNLCmUUDe7gZx8rYsg7PmvV28=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = PlansPage;\n// Função auxiliar para obter nome do módulo\nconst getModuleName = (moduleType)=>{\n    const moduleNames = {\n        'BASIC': 'Módulo Básico',\n        'ADMIN': 'Administração',\n        'SCHEDULING': 'Agendamento',\n        'PEOPLE': 'Pessoas',\n        'REPORTS': 'Relatórios',\n        'CHAT': 'Chat',\n        'ABAPLUS': 'ABA+'\n    };\n    return moduleNames[moduleType] || moduleType;\n};\n// Função auxiliar para verificar se é módulo básico\nconst isBasicModule = (moduleType)=>{\n    return [\n        'BASIC',\n        'ADMIN',\n        'SCHEDULING'\n    ].includes(moduleType);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlansPage);\nvar _c;\n$RefreshReg$(_c, \"PlansPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/modules/admin/services/plansService.js":
/*!********************************************************!*\
  !*** ./src/app/modules/admin/services/plansService.js ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   plansService: () => (/* binding */ plansService)\n/* harmony export */ });\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n// services/plansService.js\n\nconst plansService = {\n    /**\n   * Obtém dados do plano atual da empresa\n   */ async getPlansData () {\n        let companyId = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : null, forceRefresh = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data_modules, _response_data;\n            console.log('[PLANS_SERVICE] ===== INICIANDO getPlansData =====');\n            console.log('[PLANS_SERVICE] companyId:', companyId);\n            console.log('[PLANS_SERVICE] forceRefresh:', forceRefresh);\n            const params = {};\n            // Para system_admin, sempre incluir companyId se fornecido\n            if (companyId) {\n                params.companyId = companyId;\n            }\n            // Adicionar timestamp para evitar cache quando forceRefresh = true\n            if (forceRefresh) {\n                params._t = Date.now();\n                params._cache_bust = Math.random().toString(36).substring(7);\n                console.log('[PLANS_SERVICE] Cache busting params:', params);\n            }\n            const requestConfig = {\n                params\n            };\n            console.log('[PLANS_SERVICE] Request config:', requestConfig);\n            console.log('[PLANS_SERVICE] Fazendo requisição para /adminDashboard/plans...');\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.get('/adminDashboard/plans', requestConfig);\n            console.log('[PLANS_SERVICE] ===== RESPOSTA RECEBIDA =====');\n            console.log('[PLANS_SERVICE] Status:', response.status);\n            console.log('[PLANS_SERVICE] Headers:', response.headers);\n            console.log('[PLANS_SERVICE] Data:', JSON.stringify(response.data, null, 2));\n            console.log('[PLANS_SERVICE] Módulos na resposta:', (_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_modules = _response_data.modules) === null || _response_data_modules === void 0 ? void 0 : _response_data_modules.map((m)=>\"\".concat(m.moduleType, \" (\").concat(m.active ? 'ATIVO' : 'INATIVO', \")\")));\n            return response.data;\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('[PLANS_SERVICE] ===== ERRO =====');\n            console.error('Erro ao buscar dados do plano:', error);\n            console.error('Error response:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            console.error('Error status:', (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status);\n            throw error;\n        }\n    },\n    /**\n   * Obtém informações da assinatura atual\n   */ async getSubscription () {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.get('/subscription/subscription');\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao buscar assinatura:', error);\n            throw error;\n        }\n    },\n    /**\n   * Obtém planos disponíveis\n   */ async getAvailablePlans () {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.get('/subscription/plans');\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao buscar planos disponíveis:', error);\n            throw error;\n        }\n    },\n    /**\n   * Adiciona usuários ao plano\n   */ async addUsers (additionalUsers) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n        try {\n            const data = {\n                additionalUsers\n            };\n            if (companyId) {\n                data.companyId = companyId;\n            }\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.post('/subscription/users/add', data);\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao adicionar usuários:', error);\n            throw error;\n        }\n    },\n    /**\n   * Adiciona um módulo à assinatura\n   */ async addModule (moduleType) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n        try {\n            const data = {\n                moduleType\n            };\n            if (companyId) {\n                data.companyId = companyId;\n            }\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.post('/subscription/module/add', data);\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao adicionar módulo:', error);\n            throw error;\n        }\n    },\n    /**\n   * Remove um módulo da assinatura\n   */ async removeModule (moduleType) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n        try {\n            const data = {\n                moduleType\n            };\n            if (companyId) {\n                data.companyId = companyId;\n            }\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.post('/subscription/module/remove', data);\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao remover módulo:', error);\n            throw error;\n        }\n    },\n    /**\n   * Cancela a assinatura\n   */ async cancelSubscription () {\n        let companyId = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : null;\n        try {\n            const data = {};\n            if (companyId) {\n                data.companyId = companyId;\n            }\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.post('/subscription/cancel', data);\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao cancelar assinatura:', error);\n            throw error;\n        }\n    },\n    /**\n   * Reativa a assinatura\n   */ async reactivateSubscription () {\n        let companyId = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : null;\n        try {\n            const data = {};\n            if (companyId) {\n                data.companyId = companyId;\n            }\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.post('/subscription/reactivate', data);\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao reativar assinatura:', error);\n            throw error;\n        }\n    },\n    /**\n   * Faz upgrade do plano\n   */ async upgradePlan (planType, userLimit) {\n        let companyId = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : null;\n        try {\n            const data = {\n                planType,\n                userLimit\n            };\n            if (companyId) {\n                data.companyId = companyId;\n            }\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.post('/subscription/upgrade', data);\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao fazer upgrade do plano:', error);\n            throw error;\n        }\n    },\n    /**\n   * Obtém faturas com paginação\n   */ async getInvoices () {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, companyId = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : null;\n        try {\n            const params = {\n                page,\n                limit\n            };\n            if (companyId) {\n                params.companyId = companyId;\n            }\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.get('/subscription/invoices', {\n                params\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao buscar faturas:', error);\n            throw error;\n        }\n    },\n    /**\n   * Faz download de uma fatura específica\n   */ async downloadInvoice (invoiceId) {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.get(\"/subscription/invoices/\".concat(invoiceId, \"/download\"), {\n                responseType: 'blob'\n            });\n            // Cria um link temporário para download\n            const url = window.URL.createObjectURL(new Blob([\n                response.data\n            ]));\n            const link = document.createElement('a');\n            link.href = url;\n            link.setAttribute('download', \"fatura-\".concat(invoiceId, \".pdf\"));\n            document.body.appendChild(link);\n            link.click();\n            link.remove();\n            window.URL.revokeObjectURL(url);\n            return true;\n        } catch (error) {\n            console.error('Erro ao fazer download da fatura:', error);\n            throw error;\n        }\n    },\n    /**\n   * Cria sessão de checkout\n   */ async createCheckoutSession () {\n        let billingCycle = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'monthly';\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.post('/subscription/checkout', {\n                billingCycle\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao criar sessão de checkout:', error);\n            throw error;\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/services/plansService.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/api.js":
/*!**************************!*\
  !*** ./src/utils/api.js ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DOCUMENT_URLS: () => (/* binding */ DOCUMENT_URLS),\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// utils/api.js\n\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: 'http://localhost:5000',\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// Interceptor para adicionar token de autenticação em todas as requisições\napi.interceptors.request.use((config)=>{\n    const token = localStorage.getItem('token');\n    if (token) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>Promise.reject(error));\n// Interceptor para tratamento de erros\napi.interceptors.response.use((response)=>response, (error)=>{\n    var _error_response;\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n    }\n    return Promise.reject(error);\n});\n// Funções auxiliares para construir URLs\nconst getApiUrl = (path)=>{\n    // Remover barras iniciais extras se necessário\n    const cleanPath = path.startsWith('/') ? path.substring(1) : path;\n    return \"\".concat(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000', \"/\").concat(cleanPath);\n};\nconst DOCUMENT_URLS = {\n    get: (id)=>getApiUrl(\"documents/\".concat(id)),\n    download: (id)=>getApiUrl(\"documents/\".concat(id, \"?download=true\")),\n    upload: (targetType, targetId)=>getApiUrl(\"documents/upload?targetType=\".concat(targetType, \"&targetId=\").concat(targetId)),\n    list: (targetType, targetId)=>getApiUrl(\"documents?targetType=\".concat(targetType, \"&targetId=\").concat(targetId))\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/api.js\n"));

/***/ })

});