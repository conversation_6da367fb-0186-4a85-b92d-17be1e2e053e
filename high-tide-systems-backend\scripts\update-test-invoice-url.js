const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function updateTestInvoiceUrl() {
  const companyId = '9c4195cf-fe76-4455-b515-44b07224706e';
  
  console.log('Atualizando URL de uma fatura para teste de download...');

  try {
    // Buscar uma fatura paga para atualizar
    const invoice = await prisma.invoice.findFirst({
      where: {
        companyId: companyId,
        status: 'PAID'
      }
    });

    if (!invoice) {
      console.log('❌ Nenhuma fatura encontrada');
      return;
    }

    // Atualizar com uma URL de PDF público para teste
    const updatedInvoice = await prisma.invoice.update({
      where: { id: invoice.id },
      data: {
        stripeInvoiceUrl: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'
      }
    });

    console.log('✅ Fatura atualizada com sucesso!');
    console.log(`ID da fatura: ${updatedInvoice.id}`);
    console.log(`Nova URL: ${updatedInvoice.stripeInvoiceUrl}`);
    console.log('\n🎯 Agora você pode testar o download desta fatura!');

  } catch (error) {
    console.error('❌ Erro ao atualizar fatura:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateTestInvoiceUrl();
