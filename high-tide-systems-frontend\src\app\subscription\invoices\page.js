"use client";

import React, { useState, useEffect } from "react";
import { 
  FileText, 
  Download, 
  Calendar, 
  DollarSign, 
  CheckCircle, 
  Clock, 
  XCircle,
  ArrowLeft,
  Filter,
  Search,
  RefreshCw
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { plansService } from "@/app/modules/admin/services/plansService";
import { useToast } from "@/contexts/ToastContext";

const InvoicesPage = () => {
  const router = useRouter();
  const { user } = useAuth();
  const { toast_success, toast_error, toast_warning } = useToast();
  
  const [invoices, setInvoices] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [pagination, setPagination] = useState({
    total: 0,
    pages: 0,
    page: 1,
    limit: 10
  });
  const [filters, setFilters] = useState({
    status: '',
    search: ''
  });

  // Carregar faturas
  const loadInvoices = async (page = 1) => {
    try {
      setIsLoading(true);
      const response = await plansService.getInvoices(page, pagination.limit);
      setInvoices(response.invoices || []);
      setPagination(response.pagination || pagination);
    } catch (error) {
      console.error('Erro ao carregar faturas:', error);
      toast_error('Erro ao carregar faturas');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadInvoices();
  }, []);

  // Função para fazer download da fatura
  const handleDownload = async (invoice) => {
    try {
      if (!invoice.stripeInvoiceUrl && !invoice.stripeInvoiceId) {
        toast_warning('Fatura não disponível para download');
        return;
      }

      if (invoice.stripeInvoiceUrl) {
        // Se tiver URL direta, abre em nova aba
        window.open(invoice.stripeInvoiceUrl, '_blank');
      } else {
        // Senão, usa o endpoint de download
        await plansService.downloadInvoice(invoice.id);
        toast_success('Download iniciado');
      }
    } catch (error) {
      console.error('Erro ao fazer download:', error);
      toast_error('Erro ao fazer download da fatura');
    }
  };

  // Função para formatar status
  const getStatusInfo = (status) => {
    switch (status) {
      case 'PAID':
        return {
          label: 'Paga',
          icon: CheckCircle,
          className: 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20'
        };
      case 'PENDING':
        return {
          label: 'Pendente',
          icon: Clock,
          className: 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20'
        };
      case 'FAILED':
        return {
          label: 'Falhou',
          icon: XCircle,
          className: 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20'
        };
      default:
        return {
          label: status,
          icon: Clock,
          className: 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20'
        };
    }
  };

  // Função para formatar data
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  // Função para formatar valor
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  // Filtrar faturas
  const filteredInvoices = invoices.filter(invoice => {
    const matchesStatus = !filters.status || invoice.status === filters.status;
    const matchesSearch = !filters.search || 
      invoice.id.toLowerCase().includes(filters.search.toLowerCase()) ||
      formatCurrency(invoice.amount).includes(filters.search);
    
    return matchesStatus && matchesSearch;
  });

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <button
              onClick={() => router.back()}
              className="flex items-center gap-2 px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              Voltar
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                Histórico de Faturas
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Visualize e faça download das suas faturas
              </p>
            </div>
          </div>
          
          <button
            onClick={() => loadInvoices(pagination.page)}
            disabled={isLoading}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Atualizar
          </button>
        </div>

        {/* Filtros */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Buscar por ID ou valor..."
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            
            <div className="sm:w-48">
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Todos os status</option>
                <option value="PAID">Paga</option>
                <option value="PENDING">Pendente</option>
                <option value="FAILED">Falhou</option>
              </select>
            </div>
          </div>
        </div>

        {/* Lista de Faturas */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          {isLoading ? (
            <div className="flex items-center justify-center p-12">
              <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600 dark:text-gray-400">Carregando faturas...</span>
            </div>
          ) : filteredInvoices.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-12">
              <FileText className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                Nenhuma fatura encontrada
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-center">
                {filters.status || filters.search 
                  ? 'Tente ajustar os filtros para ver mais resultados.'
                  : 'Suas faturas aparecerão aqui quando forem geradas.'
                }
              </p>
            </div>
          ) : (
            <div className="overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Fatura
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Valor
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Vencimento
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Data Pagamento
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Ações
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {filteredInvoices.map((invoice) => {
                      const statusInfo = getStatusInfo(invoice.status);
                      const StatusIcon = statusInfo.icon;
                      
                      return (
                        <tr key={invoice.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <FileText className="h-5 w-5 text-gray-400 mr-3" />
                              <div>
                                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                  #{invoice.id.slice(-8)}
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                  {formatDate(invoice.createdAt)}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <DollarSign className="h-4 w-4 text-green-500 mr-1" />
                              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {formatCurrency(invoice.amount)}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.className}`}>
                              <StatusIcon className="h-3 w-3 mr-1" />
                              {statusInfo.label}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                              {formatDate(invoice.dueDate)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {invoice.paidAt ? (
                              <div className="flex items-center">
                                <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                                {formatDate(invoice.paidAt)}
                              </div>
                            ) : (
                              <span className="text-gray-400">-</span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              onClick={() => handleDownload(invoice)}
                              disabled={!invoice.stripeInvoiceUrl && !invoice.stripeInvoiceId}
                              className="inline-flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white text-sm rounded-md transition-colors disabled:cursor-not-allowed"
                            >
                              <Download className="h-4 w-4 mr-1" />
                              Download
                            </button>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>

        {/* Paginação */}
        {pagination.pages > 1 && (
          <div className="flex items-center justify-between mt-6">
            <div className="text-sm text-gray-700 dark:text-gray-300">
              Mostrando {((pagination.page - 1) * pagination.limit) + 1} a{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} de{' '}
              {pagination.total} faturas
            </div>
            
            <div className="flex items-center gap-2">
              <button
                onClick={() => loadInvoices(pagination.page - 1)}
                disabled={pagination.page <= 1 || isLoading}
                className="px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Anterior
              </button>
              
              <span className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300">
                Página {pagination.page} de {pagination.pages}
              </span>
              
              <button
                onClick={() => loadInvoices(pagination.page + 1)}
                disabled={pagination.page >= pagination.pages || isLoading}
                className="px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Próxima
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default InvoicesPage;
