const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createTestInvoices() {
  const companyId = '9c4195cf-fe76-4455-b515-44b07224706e';
  
  console.log(`Criando faturas de teste para a empresa: ${companyId}`);

  try {
    // Verificar se a empresa existe
    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: { subscription: true }
    });

    if (!company) {
      console.error('❌ Empresa não encontrada!');
      return;
    }

    console.log(`✅ Empresa encontrada: ${company.name}`);

    // Verificar se existe assinatura
    let subscription = company.subscription;
    
    if (!subscription) {
      console.log('📝 Criando assinatura para a empresa...');
      
      // Criar assinatura se não existir
      subscription = await prisma.subscription.create({
        data: {
          companyId: companyId,
          status: 'ACTIVE',
          billingCycle: 'MONTHLY',
          pricePerMonth: 299.90,
          userLimit: 50,
          startDate: new Date(),
          nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 dias
          active: true,
          cancelAtPeriodEnd: false,
          stripeCustomerId: 'cus_test_' + Math.random().toString(36).substr(2, 9),
          stripeSubscriptionId: 'sub_test_' + Math.random().toString(36).substr(2, 9),
          modules: {
            create: [
              {
                moduleType: 'BASIC',
                active: true,
                pricePerMonth: 0
              },
              {
                moduleType: 'ADMIN',
                active: true,
                pricePerMonth: 0
              },
              {
                moduleType: 'SCHEDULING',
                active: true,
                pricePerMonth: 99.90
              },
              {
                moduleType: 'RH',
                active: true,
                pricePerMonth: 79.90
              },
              {
                moduleType: 'FINANCIAL',
                active: true,
                pricePerMonth: 120.10
              }
            ]
          }
        }
      });
      
      console.log('✅ Assinatura criada com sucesso!');
    } else {
      console.log('✅ Assinatura já existe');
    }

    // Limpar faturas existentes para evitar duplicatas
    await prisma.invoice.deleteMany({
      where: { companyId: companyId }
    });

    console.log('🧹 Faturas antigas removidas');

    // Criar faturas de teste com diferentes status e datas
    const testInvoices = [
      // Fatura paga de 3 meses atrás
      {
        subscriptionId: subscription.id,
        companyId: companyId,
        amount: 299.90,
        status: 'PAID',
        dueDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
        paidAt: new Date(Date.now() - 88 * 24 * 60 * 60 * 1000),
        createdAt: new Date(Date.now() - 95 * 24 * 60 * 60 * 1000),
        stripeInvoiceId: 'in_test_' + Math.random().toString(36).substr(2, 9),
        stripeInvoiceUrl: 'https://invoice.stripe.com/i/acct_test_123/test_invoice_1'
      },
      // Fatura paga de 2 meses atrás
      {
        subscriptionId: subscription.id,
        companyId: companyId,
        amount: 299.90,
        status: 'PAID',
        dueDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
        paidAt: new Date(Date.now() - 58 * 24 * 60 * 60 * 1000),
        createdAt: new Date(Date.now() - 65 * 24 * 60 * 60 * 1000),
        stripeInvoiceId: 'in_test_' + Math.random().toString(36).substr(2, 9),
        stripeInvoiceUrl: 'https://invoice.stripe.com/i/acct_test_123/test_invoice_2'
      },
      // Fatura paga do mês passado
      {
        subscriptionId: subscription.id,
        companyId: companyId,
        amount: 299.90,
        status: 'PAID',
        dueDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        paidAt: new Date(Date.now() - 28 * 24 * 60 * 60 * 1000),
        createdAt: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000),
        stripeInvoiceId: 'in_test_' + Math.random().toString(36).substr(2, 9),
        stripeInvoiceUrl: 'https://invoice.stripe.com/i/acct_test_123/test_invoice_3'
      },
      // Fatura pendente do mês atual
      {
        subscriptionId: subscription.id,
        companyId: companyId,
        amount: 299.90,
        status: 'PENDING',
        dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
        paidAt: null,
        createdAt: new Date(),
        stripeInvoiceId: 'in_test_' + Math.random().toString(36).substr(2, 9),
        stripeInvoiceUrl: 'https://invoice.stripe.com/i/acct_test_123/test_invoice_4'
      },
      // Fatura que falhou
      {
        subscriptionId: subscription.id,
        companyId: companyId,
        amount: 299.90,
        status: 'FAILED',
        dueDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
        paidAt: null,
        createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
        stripeInvoiceId: 'in_test_' + Math.random().toString(36).substr(2, 9),
        stripeInvoiceUrl: 'https://invoice.stripe.com/i/acct_test_123/test_invoice_5'
      },
      // Fatura com valor diferente (upgrade)
      {
        subscriptionId: subscription.id,
        companyId: companyId,
        amount: 399.90,
        status: 'PAID',
        dueDate: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
        paidAt: new Date(Date.now() - 43 * 24 * 60 * 60 * 1000),
        createdAt: new Date(Date.now() - 50 * 24 * 60 * 60 * 1000),
        stripeInvoiceId: 'in_test_' + Math.random().toString(36).substr(2, 9),
        stripeInvoiceUrl: 'https://invoice.stripe.com/i/acct_test_123/test_invoice_6'
      },
      // Fatura mais antiga
      {
        subscriptionId: subscription.id,
        companyId: companyId,
        amount: 199.90,
        status: 'PAID',
        dueDate: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000),
        paidAt: new Date(Date.now() - 118 * 24 * 60 * 60 * 1000),
        createdAt: new Date(Date.now() - 125 * 24 * 60 * 60 * 1000),
        stripeInvoiceId: 'in_test_' + Math.random().toString(36).substr(2, 9),
        stripeInvoiceUrl: 'https://invoice.stripe.com/i/acct_test_123/test_invoice_7'
      }
    ];

    // Criar todas as faturas
    const createdInvoices = await Promise.all(
      testInvoices.map(invoiceData => 
        prisma.invoice.create({ data: invoiceData })
      )
    );

    console.log(`✅ ${createdInvoices.length} faturas de teste criadas com sucesso!`);
    
    // Mostrar resumo
    console.log('\n📊 Resumo das faturas criadas:');
    const statusCount = createdInvoices.reduce((acc, invoice) => {
      acc[invoice.status] = (acc[invoice.status] || 0) + 1;
      return acc;
    }, {});
    
    Object.entries(statusCount).forEach(([status, count]) => {
      console.log(`   ${status}: ${count} faturas`);
    });

    console.log('\n🎉 Processo concluído!');
    console.log(`Empresa ID: ${companyId}`);
    console.log(`Empresa: ${company.name}`);
    console.log('Agora você pode testar a tela de faturas!');

  } catch (error) {
    console.error('❌ Erro ao criar faturas de teste:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestInvoices();
