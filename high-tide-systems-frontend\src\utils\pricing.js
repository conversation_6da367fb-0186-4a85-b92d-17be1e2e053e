// Configuração de preços centralizada (deve ser igual ao backend)
export const PRICING_CONFIG = {
  basePrice: 19.90, // Preço base por usuário/mês
  annualDiscount: 0.10, // 10% de desconto para pagamento anual
  
  // Descontos por quantidade de usuários
  userDiscounts: [
    { minUsers: 200, discount: 40 },
    { minUsers: 100, discount: 35 },
    { minUsers: 50, discount: 25 },
    { minUsers: 20, discount: 15 },
    { minUsers: 5, discount: 10 },
    { minUsers: 1, discount: 0 }
  ]
};

/**
 * Calcula desconto baseado na quantidade de usuários
 * @param {number} users - Quantidade de usuários
 * @returns {number} - Percentual de desconto (0-40)
 */
export const getDiscountByUserCount = (users) => {
  for (const tier of PRICING_CONFIG.userDiscounts) {
    if (users >= tier.minUsers) {
      return tier.discount;
    }
  }
  return 0;
};

/**
 * Calcula preços completos baseado na quantidade de usuários
 * @param {number} users - Quantidade de usuários
 * @param {boolean} isAnnual - Se é pagamento anual
 * @returns {Object} - Objeto com todos os cálculos de preço
 */
export const calculatePrice = (users, isAnnual = false) => {
  const discount = getDiscountByUserCount(users);
  const totalWithoutDiscount = users * PRICING_CONFIG.basePrice;
  const discountAmount = totalWithoutDiscount * (discount / 100);
  const monthlyPrice = totalWithoutDiscount - discountAmount;
  
  // Preço anual com desconto adicional
  const yearlyPriceWithoutAnnualDiscount = monthlyPrice * 12;
  const yearlyPrice = yearlyPriceWithoutAnnualDiscount * (1 - PRICING_CONFIG.annualDiscount);
  
  return {
    totalWithoutDiscount,
    discountAmount,
    discount,
    monthlyPrice,
    yearlyPrice,
    yearlyPriceWithoutAnnualDiscount,
    annualDiscount: PRICING_CONFIG.annualDiscount * 100,
    pricePerUser: monthlyPrice / users,
    finalPrice: isAnnual ? yearlyPrice : monthlyPrice,
    annualSavings: yearlyPriceWithoutAnnualDiscount - yearlyPrice
  };
};

/**
 * Formata valor monetário para exibição
 * @param {number} value - Valor a ser formatado
 * @returns {string} - Valor formatado em R$
 */
export const formatCurrency = (value) => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
};

/**
 * Calcula economia total (desconto por quantidade + desconto anual)
 * @param {number} users - Quantidade de usuários
 * @param {boolean} isAnnual - Se é pagamento anual
 * @returns {Object} - Objeto com informações de economia
 */
export const calculateSavings = (users, isAnnual = false) => {
  const pricing = calculatePrice(users, isAnnual);
  const fullPriceMonthly = users * PRICING_CONFIG.basePrice;
  const fullPriceYearly = fullPriceMonthly * 12;
  
  let totalSavings = 0;
  let savingsBreakdown = [];
  
  // Economia por quantidade
  if (pricing.discount > 0) {
    const quantitySavings = isAnnual 
      ? pricing.discountAmount * 12 
      : pricing.discountAmount;
    
    totalSavings += quantitySavings;
    savingsBreakdown.push({
      type: 'quantity',
      label: `Desconto por ${users} usuários (${pricing.discount}%)`,
      amount: quantitySavings,
      monthly: pricing.discountAmount
    });
  }
  
  // Economia anual
  if (isAnnual) {
    const annualSavings = pricing.annualSavings;
    totalSavings += annualSavings;
    savingsBreakdown.push({
      type: 'annual',
      label: `Desconto anual (${pricing.annualDiscount}%)`,
      amount: annualSavings,
      monthly: annualSavings / 12
    });
  }
  
  return {
    totalSavings,
    savingsBreakdown,
    fullPrice: isAnnual ? fullPriceYearly : fullPriceMonthly,
    finalPrice: pricing.finalPrice,
    savingsPercentage: totalSavings > 0 ? (totalSavings / (isAnnual ? fullPriceYearly : fullPriceMonthly)) * 100 : 0
  };
};

/**
 * Opções de usuários predefinidas para seleção
 */
export const USER_OPTIONS = [1, 5, 20, 50, 100, 200];

/**
 * Valida se a quantidade de usuários está dentro dos limites
 * @param {number} users - Quantidade de usuários
 * @returns {boolean} - Se é válido
 */
export const isValidUserCount = (users) => {
  return users >= 1 && users <= 1000 && Number.isInteger(users);
};

/**
 * Obtém a próxima faixa de desconto
 * @param {number} currentUsers - Quantidade atual de usuários
 * @returns {Object|null} - Próxima faixa ou null se já está na máxima
 */
export const getNextDiscountTier = (currentUsers) => {
  for (const tier of PRICING_CONFIG.userDiscounts) {
    if (currentUsers < tier.minUsers) {
      return {
        minUsers: tier.minUsers,
        discount: tier.discount,
        usersToNext: tier.minUsers - currentUsers
      };
    }
  }
  return null;
};

/**
 * Calcula o custo adicional para adicionar mais usuários
 * @param {number} currentUsers - Quantidade atual de usuários
 * @param {number} additionalUsers - Usuários a adicionar
 * @param {boolean} isAnnual - Se é pagamento anual
 * @returns {Object} - Informações sobre o custo adicional
 */
export const calculateAdditionalUsersCost = (currentUsers, additionalUsers, isAnnual = false) => {
  const currentPricing = calculatePrice(currentUsers, isAnnual);
  const newPricing = calculatePrice(currentUsers + additionalUsers, isAnnual);
  
  const additionalCost = newPricing.finalPrice - currentPricing.finalPrice;
  const costPerAdditionalUser = additionalCost / additionalUsers;
  
  return {
    currentPrice: currentPricing.finalPrice,
    newPrice: newPricing.finalPrice,
    additionalCost,
    costPerAdditionalUser,
    newDiscount: newPricing.discount,
    discountImprovement: newPricing.discount - currentPricing.discount
  };
};
