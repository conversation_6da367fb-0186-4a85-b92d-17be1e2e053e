// services/plansService.js
import { api } from '@/utils/api';

export const plansService = {
  /**
   * Obtém dados do plano atual da empresa
   */
  async getPlansData(companyId = null, forceRefresh = false) {
    try {
      console.log('[PLANS_SERVICE] ===== INICIANDO getPlansData =====');
      console.log('[PLANS_SERVICE] companyId:', companyId);
      console.log('[PLANS_SERVICE] forceRefresh:', forceRefresh);

      const params = {};

      // Para system_admin, sempre incluir companyId se fornecido
      if (companyId) {
        params.companyId = companyId;
      }

      // Adicionar timestamp para evitar cache quando forceRefresh = true
      if (forceRefresh) {
        params._t = Date.now();
        params._cache_bust = Math.random().toString(36).substring(7);
        console.log('[PLANS_SERVICE] Cache busting params:', params);
      }

      const requestConfig = {
        params
        // Removendo headers customizados para evitar problemas de CORS
        // Os parâmetros _t e _cache_bust já são suficientes para evitar cache
      };

      console.log('[PLANS_SERVICE] Request config:', requestConfig);
      console.log('[PLANS_SERVICE] Fazendo requisição para /adminDashboard/plans...');

      const response = await api.get('/adminDashboard/plans', requestConfig);

      console.log('[PLANS_SERVICE] ===== RESPOSTA RECEBIDA =====');
      console.log('[PLANS_SERVICE] Status:', response.status);
      console.log('[PLANS_SERVICE] Headers:', response.headers);
      console.log('[PLANS_SERVICE] Data:', JSON.stringify(response.data, null, 2));
      console.log('[PLANS_SERVICE] Módulos na resposta:', response.data?.modules?.map(m => `${m.moduleType} (${m.active ? 'ATIVO' : 'INATIVO'})`));

      return response.data;
    } catch (error) {
      console.error('[PLANS_SERVICE] ===== ERRO =====');
      console.error('Erro ao buscar dados do plano:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      throw error;
    }
  },

  /**
   * Obtém informações da assinatura atual
   */
  async getSubscription() {
    try {
      const response = await api.get('/subscription/subscription');
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar assinatura:', error);
      throw error;
    }
  },

  /**
   * Obtém planos disponíveis
   */
  async getAvailablePlans() {
    try {
      const response = await api.get('/subscription/plans');
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar planos disponíveis:', error);
      throw error;
    }
  },

  /**
   * Adiciona usuários ao plano
   */
  async addUsers(additionalUsers, companyId = null) {
    try {
      const data = { additionalUsers };
      if (companyId) {
        data.companyId = companyId;
      }

      const response = await api.post('/subscription/users/add', data);
      return response.data;
    } catch (error) {
      console.error('Erro ao adicionar usuários:', error);
      throw error;
    }
  },

  /**
   * Adiciona um módulo à assinatura
   */
  async addModule(moduleType, companyId = null) {
    try {
      const data = { moduleType };
      if (companyId) {
        data.companyId = companyId;
      }

      const response = await api.post('/subscription/module/add', data);
      return response.data;
    } catch (error) {
      console.error('Erro ao adicionar módulo:', error);
      throw error;
    }
  },

  /**
   * Remove um módulo da assinatura
   */
  async removeModule(moduleType, companyId = null) {
    try {
      const data = { moduleType };
      if (companyId) {
        data.companyId = companyId;
      }

      const response = await api.post('/subscription/module/remove', data);
      return response.data;
    } catch (error) {
      console.error('Erro ao remover módulo:', error);
      throw error;
    }
  },

  /**
   * Cancela a assinatura
   */
  async cancelSubscription(companyId = null) {
    try {
      const data = {};
      if (companyId) {
        data.companyId = companyId;
      }

      const response = await api.post('/subscription/cancel', data);
      return response.data;
    } catch (error) {
      console.error('Erro ao cancelar assinatura:', error);
      throw error;
    }
  },

  /**
   * Reativa a assinatura
   */
  async reactivateSubscription(companyId = null) {
    try {
      const data = {};
      if (companyId) {
        data.companyId = companyId;
      }

      const response = await api.post('/subscription/reactivate', data);
      return response.data;
    } catch (error) {
      console.error('Erro ao reativar assinatura:', error);
      throw error;
    }
  },

  /**
   * Faz upgrade do plano
   */
  async upgradePlan(planType, userLimit, companyId = null) {
    try {
      const data = { planType, userLimit };
      if (companyId) {
        data.companyId = companyId;
      }

      const response = await api.post('/subscription/upgrade', data);
      return response.data;
    } catch (error) {
      console.error('Erro ao fazer upgrade do plano:', error);
      throw error;
    }
  },

  /**
   * Obtém faturas com paginação
   */
  async getInvoices(page = 1, limit = 10, companyId = null) {
    try {
      const params = { page, limit };
      if (companyId) {
        params.companyId = companyId;
      }

      const response = await api.get('/subscription/invoices', { params });
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar faturas:', error);
      throw error;
    }
  },

  /**
   * Faz download de uma fatura específica
   */
  async downloadInvoice(invoiceId) {
    try {
      const response = await api.get(`/subscription/invoices/${invoiceId}/download`, {
        responseType: 'blob'
      });

      // Cria um link temporário para download
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `fatura-${invoiceId}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      return true;
    } catch (error) {
      console.error('Erro ao fazer download da fatura:', error);
      throw error;
    }
  },

  /**
   * Cria sessão de checkout
   */
  async createCheckoutSession(billingCycle = 'monthly') {
    try {
      const response = await api.post('/subscription/checkout', {
        billingCycle
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao criar sessão de checkout:', error);
      throw error;
    }
  }
};
