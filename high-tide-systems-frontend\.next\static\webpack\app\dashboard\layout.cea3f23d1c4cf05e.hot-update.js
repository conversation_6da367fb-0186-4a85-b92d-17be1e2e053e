"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/utils/api.js":
/*!**************************!*\
  !*** ./src/utils/api.js ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DOCUMENT_URLS: () => (/* binding */ DOCUMENT_URLS),\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// utils/api.js\n\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: 'http://localhost:5000',\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// Interceptor para adicionar token de autenticação em todas as requisições\napi.interceptors.request.use((config)=>{\n    const token = localStorage.getItem('token');\n    if (token) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>Promise.reject(error));\n// Interceptor para tratamento de erros\napi.interceptors.response.use((response)=>response, (error)=>{\n    var _error_response;\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n        var _error_config, _error_config1, _error_response1, _error_response2, _error_config2;\n        console.error('[API] Erro 401 detectado:', {\n            url: (_error_config = error.config) === null || _error_config === void 0 ? void 0 : _error_config.url,\n            method: (_error_config1 = error.config) === null || _error_config1 === void 0 ? void 0 : _error_config1.method,\n            status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status,\n            data: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n            headers: (_error_config2 = error.config) === null || _error_config2 === void 0 ? void 0 : _error_config2.headers\n        });\n    // Temporariamente comentado para debug\n    // localStorage.removeItem('token');\n    // window.location.href = '/login';\n    }\n    return Promise.reject(error);\n});\n// Funções auxiliares para construir URLs\nconst getApiUrl = (path)=>{\n    // Remover barras iniciais extras se necessário\n    const cleanPath = path.startsWith('/') ? path.substring(1) : path;\n    return \"\".concat(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000', \"/\").concat(cleanPath);\n};\nconst DOCUMENT_URLS = {\n    get: (id)=>getApiUrl(\"documents/\".concat(id)),\n    download: (id)=>getApiUrl(\"documents/\".concat(id, \"?download=true\")),\n    upload: (targetType, targetId)=>getApiUrl(\"documents/upload?targetType=\".concat(targetType, \"&targetId=\").concat(targetId)),\n    list: (targetType, targetId)=>getApiUrl(\"documents?targetType=\".concat(targetType, \"&targetId=\").concat(targetId))\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/api.js\n"));

/***/ })

});