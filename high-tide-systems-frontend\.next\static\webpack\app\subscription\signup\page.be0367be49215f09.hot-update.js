"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/subscription/signup/page",{

/***/ "(app-pages-browser)/./src/app/subscription/signup/page.js":
/*!*********************************************!*\
  !*** ./src/app/subscription/signup/page.js ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SubscriptionSignupPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_input_mask__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @react-input/mask */ \"(app-pages-browser)/./node_modules/@react-input/mask/module/InputMask.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Calendar,CheckCircle,CreditCard,Lock,Mail,MapPin,Phone,User,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _services_subscriptionService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/subscriptionService */ \"(app-pages-browser)/./src/services/subscriptionService.js\");\n/* harmony import */ var _utils_pricing__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/pricing */ \"(app-pages-browser)/./src/utils/pricing.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SubscriptionSignupPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [documentType, setDocumentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"cpf\");\n    const [billingCycle, setBillingCycle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"monthly\");\n    const [userCount, setUserCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5); // Número de usuários selecionado\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Dados pessoais\n        login: \"\",\n        fullName: \"\",\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        cpf: \"\",\n        cnpj: \"\",\n        birthDate: \"\",\n        address: \"\",\n        phone: \"\",\n        // Dados da empresa\n        companyName: \"\",\n        companyTradingName: \"\",\n        companyCnpj: \"\",\n        companyPhone: \"\",\n        companyAddress: \"\",\n        companyCity: \"\",\n        companyState: \"\",\n        companyPostalCode: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const basePrice = 19.90; // Preço base por usuário\n    // Detecta se está em ambiente de desenvolvimento (localhost)\n    const isDevelopment =  true && (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');\n    // Dados de teste para desenvolvimento\n    const devData = {\n        login: \"teste_dev\",\n        fullName: \"Usuário de Teste\",\n        email: \"<EMAIL>\",\n        password: \"123456\",\n        confirmPassword: \"123456\",\n        cpf: \"123.456.789-00\",\n        cnpj: \"12.345.678/0001-90\",\n        birthDate: \"1990-01-01\",\n        address: \"Rua de Teste, 123\",\n        phone: \"(11) 99999-9999\",\n        companyName: \"Empresa de Teste Ltda\",\n        companyTradingName: \"Teste Corp\",\n        companyCnpj: \"98.765.432/0001-10\",\n        companyPhone: \"(11) 88888-8888\",\n        companyAddress: \"Av. Empresarial, 456\",\n        companyCity: \"São Paulo\",\n        companyState: \"SP\",\n        companyPostalCode: \"01234-567\"\n    };\n    // Função para calcular desconto baseado na quantidade de usuários\n    const getDiscountByUserCount = (users)=>{\n        if (users >= 200) return 40;\n        if (users >= 100) return 35;\n        if (users >= 50) return 25;\n        if (users >= 20) return 15;\n        if (users >= 5) return 10;\n        return 0;\n    };\n    // Função para calcular preços\n    const calculatePrice = (users)=>{\n        const discount = getDiscountByUserCount(users);\n        const totalWithoutDiscount = users * basePrice;\n        const discountAmount = totalWithoutDiscount * (discount / 100);\n        const finalPrice = totalWithoutDiscount - discountAmount;\n        // Calcular preço anual com desconto de 20% (equivalente a 10 meses)\n        const yearlyPriceWithoutAnnualDiscount = finalPrice * 12;\n        const yearlyPrice = finalPrice * 10; // 20% de desconto anual (2 meses grátis)\n        const annualSavings = yearlyPriceWithoutAnnualDiscount - yearlyPrice;\n        return {\n            totalWithoutDiscount,\n            discountAmount,\n            finalPrice,\n            discount,\n            monthlyPrice: finalPrice,\n            yearlyPrice,\n            yearlyPriceWithoutAnnualDiscount,\n            annualSavings\n        };\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Limpa o erro do campo quando o usuário começa a digitar\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: undefined\n                }));\n        }\n    };\n    const removeMask = (value)=>{\n        return value ? value.replace(/\\D/g, \"\") : \"\";\n    };\n    // Função para preencher dados de desenvolvimento\n    const fillDevData = ()=>{\n        if (isDevelopment) {\n            setFormData(devData);\n        }\n    };\n    // Função para pular para o próximo passo (apenas em desenvolvimento)\n    const skipToStep = (step)=>{\n        if (isDevelopment) {\n            if (step >= 2 && currentStep === 1) {\n                fillDevData();\n            }\n            setCurrentStep(step);\n        }\n    };\n    const validateStep1 = ()=>{\n        const newErrors = {};\n        if (!formData.login) newErrors.login = \"Login é obrigatório\";\n        if (!formData.fullName) newErrors.fullName = \"Nome é obrigatório\";\n        if (!formData.email || !/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = \"Email inválido\";\n        }\n        if (!formData.password || formData.password.length < 6) {\n            newErrors.password = \"Senha deve ter no mínimo 6 caracteres\";\n        }\n        if (formData.password !== formData.confirmPassword) {\n            newErrors.confirmPassword = \"Senhas não conferem\";\n        }\n        if (documentType === \"cpf\" && !formData.cpf) {\n            newErrors.cpf = \"CPF é obrigatório\";\n        }\n        if (documentType === \"cnpj\" && !formData.cnpj) {\n            newErrors.cnpj = \"CNPJ é obrigatório\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const validateStep2 = ()=>{\n        const newErrors = {};\n        if (!formData.companyName) newErrors.companyName = \"Nome da empresa é obrigatório\";\n        if (!formData.companyCnpj) newErrors.companyCnpj = \"CNPJ da empresa é obrigatório\";\n        if (!formData.companyPhone) newErrors.companyPhone = \"Telefone da empresa é obrigatório\";\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleNextStep = ()=>{\n        if (currentStep === 1 && validateStep1()) {\n            setCurrentStep(2);\n        } else if (currentStep === 2 && validateStep2()) {\n            setCurrentStep(3);\n        }\n    };\n    const handlePrevStep = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const handleSubmit = async ()=>{\n        if (!validateStep2()) return;\n        setIsLoading(true);\n        try {\n            // 1. Registra o usuário e empresa em uma única operação\n            const userResponse = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.post(\"/auth/register\", {\n                // Dados do usuário\n                login: formData.login,\n                fullName: formData.fullName,\n                email: formData.email,\n                password: formData.password,\n                cpf: documentType === \"cpf\" ? removeMask(formData.cpf) : undefined,\n                cnpj: documentType === \"cnpj\" ? removeMask(formData.cnpj) : undefined,\n                birthDate: formData.birthDate || undefined,\n                address: formData.address || undefined,\n                phone: formData.phone ? removeMask(formData.phone) : undefined,\n                // Dados da empresa\n                companyName: formData.companyName,\n                companyTradingName: formData.companyTradingName,\n                companyCnpj: formData.companyCnpj,\n                companyPhone: formData.companyPhone,\n                companyAddress: formData.companyAddress,\n                companyCity: formData.companyCity,\n                companyState: formData.companyState,\n                companyPostalCode: formData.companyPostalCode\n            });\n            // 2. Salva o token de autenticação\n            if (userResponse.data.token) {\n                localStorage.setItem('token', userResponse.data.token);\n            }\n            // 3. Cria a sessão de checkout do Stripe\n            const pricing = calculatePrice(userCount);\n            const checkoutResponse = await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_6__.subscriptionService.createCheckoutSession({\n                billingCycle,\n                users: userCount,\n                price: billingCycle === 'monthly' ? pricing.monthlyPrice : pricing.yearlyPrice,\n                discount: pricing.discount\n            });\n            // 4. Redireciona para o Stripe Checkout\n            if (checkoutResponse.url) {\n                window.location.href = checkoutResponse.url;\n            }\n        } catch (error) {\n            setErrors((prev)=>{\n                var _error_response_data, _error_response;\n                return {\n                    ...prev,\n                    submit: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Erro ao processar cadastro\"\n                };\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const inputClasses = \"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 dark:text-white bg-white dark:bg-gray-700\";\n    const labelClasses = \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\";\n    const iconContainerClasses = \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-xl w-full max-w-4xl p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-12 w-12 text-orange-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"mx-4 text-3xl font-bold text-gray-900 dark:text-white\",\n                                    children: \"Criar conta e assinar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center space-x-4 mb-6\",\n                            children: [\n                                1,\n                                2,\n                                3\n                            ].map((step)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium \".concat(currentStep >= step ? 'bg-orange-500 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300'),\n                                            children: currentStep > step ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 293,\n                                                columnNumber: 41\n                                            }, this) : step\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this),\n                                        step < 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-1 mx-2 \".concat(currentStep > step ? 'bg-orange-500' : 'bg-gray-200 dark:bg-gray-600')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 296,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, step, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                            children: [\n                                currentStep === 1 && \"Dados pessoais\",\n                                currentStep === 2 && \"Dados da empresa\",\n                                currentStep === 3 && \"Plano e pagamento\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this),\n                        isDevelopment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-yellow-800 dark:text-yellow-300 mb-2 text-center\",\n                                    children: \"\\uD83D\\uDEA7 Modo Desenvolvimento - Atalhos para testes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: fillDevData,\n                                            className: \"px-3 py-1 bg-yellow-500 text-white rounded text-xs hover:bg-yellow-600\",\n                                            children: \"Preencher Dados\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>skipToStep(1),\n                                            className: \"px-3 py-1 rounded text-xs \".concat(currentStep === 1 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                            children: \"Passo 1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 324,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>skipToStep(2),\n                                            className: \"px-3 py-1 rounded text-xs \".concat(currentStep === 2 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                            children: \"Passo 2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>skipToStep(3),\n                                            className: \"px-3 py-1 rounded text-xs \".concat(currentStep === 3 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                            children: \"Passo 3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 312,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this),\n                errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg mb-6\",\n                    children: errors.submit\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                    lineNumber: 351,\n                    columnNumber: 11\n                }, this),\n                currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"login\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"login\",\n                                                    name: \"login\",\n                                                    type: \"text\",\n                                                    value: formData.login,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.login && \"border-red-500\"),\n                                                    placeholder: \"Seu login\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.login && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.login\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 379,\n                                            columnNumber: 34\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 361,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"fullName\",\n                                            children: \"Nome Completo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 384,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"fullName\",\n                                                    name: \"fullName\",\n                                                    type: \"text\",\n                                                    value: formData.fullName,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.fullName && \"border-red-500\"),\n                                                    placeholder: \"Seu nome completo\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.fullName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.fullName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 401,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 359,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"email\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 408,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"email\",\n                                                    name: \"email\",\n                                                    type: \"email\",\n                                                    value: formData.email,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.email && \"border-red-500\"),\n                                                    placeholder: \"<EMAIL>\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 425,\n                                            columnNumber: 34\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"password\",\n                                            children: \"Senha\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: \"password\",\n                                                    value: formData.password,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.password && \"border-red-500\"),\n                                                    placeholder: \"••••••••\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.password\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 446,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 406,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"confirmPassword\",\n                                            children: \"Confirmar Senha\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"confirmPassword\",\n                                                    name: \"confirmPassword\",\n                                                    type: \"password\",\n                                                    value: formData.confirmPassword,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.confirmPassword && \"border-red-500\"),\n                                                    placeholder: \"••••••••\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 454,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.confirmPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.confirmPassword\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 470,\n                                            columnNumber: 44\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 452,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            children: \"Tipo de Documento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 474,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"documentType\",\n                                                            value: \"cpf\",\n                                                            checked: documentType === \"cpf\",\n                                                            onChange: (e)=>setDocumentType(e.target.value),\n                                                            className: \"mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"CPF\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"documentType\",\n                                                            value: \"cnpj\",\n                                                            checked: documentType === \"cnpj\",\n                                                            onChange: (e)=>setDocumentType(e.target.value),\n                                                            className: \"mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"CNPJ\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 475,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 473,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 451,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: documentType,\n                                            children: documentType === \"cpf\" ? \"CPF\" : \"CNPJ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 505,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_input_mask__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    mask: documentType === \"cpf\" ? \"___.___.___-__\" : \"__.___.___/____-__\",\n                                                    replacement: {\n                                                        _: /\\d/\n                                                    },\n                                                    name: documentType,\n                                                    value: formData[documentType],\n                                                    onChange: handleChange,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors[documentType] && \"border-red-500\"),\n                                                    placeholder: documentType === \"cpf\" ? \"000.000.000-00\" : \"00.000.000/0000-00\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 508,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors[documentType] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors[documentType]\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 523,\n                                            columnNumber: 42\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 504,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"phone\",\n                                            children: \"Telefone (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 527,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_input_mask__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    mask: \"(__) _____-____\",\n                                                    replacement: {\n                                                        _: /\\d/\n                                                    },\n                                                    name: \"phone\",\n                                                    value: formData.phone,\n                                                    onChange: handleChange,\n                                                    className: inputClasses,\n                                                    placeholder: \"(11) 99999-9999\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 528,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 526,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 503,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"birthDate\",\n                                            children: \"Data de Nascimento (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 549,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"birthDate\",\n                                                    name: \"birthDate\",\n                                                    type: \"date\",\n                                                    value: formData.birthDate,\n                                                    onChange: handleChange,\n                                                    className: inputClasses,\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 548,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"address\",\n                                            children: \"Endere\\xe7o (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 567,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"address\",\n                                                    name: \"address\",\n                                                    type: \"text\",\n                                                    value: formData.address,\n                                                    onChange: handleChange,\n                                                    className: inputClasses,\n                                                    placeholder: \"Seu endere\\xe7o\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 568,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 566,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 547,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between pt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/landing\",\n                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 591,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Voltar\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 587,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleNextStep,\n                                    className: \"inline-flex items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600\",\n                                    children: [\n                                        \"Pr\\xf3ximo\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-4 h-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 601,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 595,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 586,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                    lineNumber: 358,\n                    columnNumber: 11\n                }, this),\n                currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyName\",\n                                            children: \"Nome da Empresa\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 613,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"companyName\",\n                                                    name: \"companyName\",\n                                                    type: \"text\",\n                                                    value: formData.companyName,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.companyName && \"border-red-500\"),\n                                                    placeholder: \"Raz\\xe3o social da empresa\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 618,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 614,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.companyName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.companyName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 630,\n                                            columnNumber: 40\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 612,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyTradingName\",\n                                            children: \"Nome Fantasia (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 634,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"companyTradingName\",\n                                                    name: \"companyTradingName\",\n                                                    type: \"text\",\n                                                    value: formData.companyTradingName,\n                                                    onChange: handleChange,\n                                                    className: inputClasses,\n                                                    placeholder: \"Nome fantasia\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 639,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 635,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 633,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 611,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyCnpj\",\n                                            children: \"CNPJ da Empresa\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 656,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 658,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_input_mask__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    mask: \"__.___.___/____-__\",\n                                                    replacement: {\n                                                        _: /\\d/\n                                                    },\n                                                    name: \"companyCnpj\",\n                                                    value: formData.companyCnpj,\n                                                    onChange: handleChange,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.companyCnpj && \"border-red-500\"),\n                                                    placeholder: \"00.000.000/0000-00\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 657,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.companyCnpj && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.companyCnpj\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 672,\n                                            columnNumber: 40\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 655,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyPhone\",\n                                            children: \"Telefone da Empresa\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 676,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: iconContainerClasses,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_input_mask__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    mask: \"(__) _____-____\",\n                                                    replacement: {\n                                                        _: /\\d/\n                                                    },\n                                                    name: \"companyPhone\",\n                                                    value: formData.companyPhone,\n                                                    onChange: handleChange,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(inputClasses, errors.companyPhone && \"border-red-500\"),\n                                                    placeholder: \"(11) 99999-9999\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 677,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.companyPhone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-xs text-red-600\",\n                                            children: errors.companyPhone\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 692,\n                                            columnNumber: 41\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 675,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 654,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: labelClasses,\n                                    htmlFor: \"companyAddress\",\n                                    children: \"Endere\\xe7o da Empresa (opcional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 698,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: iconContainerClasses,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 701,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 700,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"companyAddress\",\n                                            name: \"companyAddress\",\n                                            type: \"text\",\n                                            value: formData.companyAddress,\n                                            onChange: handleChange,\n                                            className: inputClasses,\n                                            placeholder: \"Endere\\xe7o completo da empresa\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 703,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 699,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 697,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyCity\",\n                                            children: \"Cidade (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 719,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"companyCity\",\n                                            name: \"companyCity\",\n                                            type: \"text\",\n                                            value: formData.companyCity,\n                                            onChange: handleChange,\n                                            className: inputClasses,\n                                            placeholder: \"Cidade\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 720,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 718,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyState\",\n                                            children: \"Estado (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 733,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"companyState\",\n                                            name: \"companyState\",\n                                            type: \"text\",\n                                            value: formData.companyState,\n                                            onChange: handleChange,\n                                            className: inputClasses,\n                                            placeholder: \"Estado\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 734,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 732,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: labelClasses,\n                                            htmlFor: \"companyPostalCode\",\n                                            children: \"CEP (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 747,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_input_mask__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            mask: \"_____-___\",\n                                            replacement: {\n                                                _: /\\d/\n                                            },\n                                            name: \"companyPostalCode\",\n                                            value: formData.companyPostalCode,\n                                            onChange: handleChange,\n                                            className: inputClasses,\n                                            placeholder: \"00000-000\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 748,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 746,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 717,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between pt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handlePrevStep,\n                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 767,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Anterior\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 762,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleNextStep,\n                                    className: \"inline-flex items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600\",\n                                    children: [\n                                        \"Pr\\xf3ximo\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-4 h-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 777,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 771,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 761,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                    lineNumber: 609,\n                    columnNumber: 11\n                }, this),\n                currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        isDevelopment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-yellow-400\",\n                                            viewBox: \"0 0 20 20\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 792,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 791,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                        lineNumber: 790,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-yellow-800 dark:text-yellow-300\",\n                                                children: \"Modo Desenvolvimento Ativo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 796,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 text-sm text-yellow-700 dark:text-yellow-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Modo desenvolvimento ativo. Os dados dos formul\\xe1rios foram pr\\xe9-preenchidos para agilizar os testes. O fluxo de pagamento funcionar\\xe1 normalmente e voc\\xea ser\\xe1 redirecionado para o Stripe.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 799,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                        lineNumber: 795,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                lineNumber: 789,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 788,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: \"Escolha seu plano\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 811,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center items-center space-x-4 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm \".concat(billingCycle === 'monthly' ? 'text-orange-600 font-medium' : 'text-gray-500 dark:text-gray-400'),\n                                            children: \"Mensal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 813,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly'),\n                                            className: \"relative inline-flex h-6 w-11 items-center rounded-full transition-colors \".concat(billingCycle === 'yearly' ? 'bg-orange-500' : 'bg-gray-200 dark:bg-gray-600'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block h-4 w-4 transform rounded-full bg-white transition-transform \".concat(billingCycle === 'yearly' ? 'translate-x-6' : 'translate-x-1')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 823,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 816,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm \".concat(billingCycle === 'yearly' ? 'text-orange-600 font-medium' : 'text-gray-500 dark:text-gray-400'),\n                                            children: [\n                                                \"Anual \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-500 text-xs font-medium\",\n                                                    children: \"(2 meses gr\\xe1tis)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 830,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 829,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 812,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 810,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-700 rounded-xl border-2 border-gray-200 dark:border-gray-600 p-6 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                            children: \"Quantos usu\\xe1rios voc\\xea precisa?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 838,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: \"Selecione a quantidade de usu\\xe1rios que ter\\xe3o acesso ao sistema\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 841,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 837,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full max-w-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"1\",\n                                                    max: \"200\",\n                                                    value: userCount,\n                                                    onChange: (e)=>setUserCount(parseInt(e.target.value)),\n                                                    className: \"w-full h-2 rounded-lg appearance-none cursor-pointer\",\n                                                    style: {\n                                                        background: \"linear-gradient(to right, #f97316 0%, #f97316 \".concat(userCount / 200 * 100, \"%, #d1d5db \").concat(userCount / 200 * 100, \"%, #d1d5db 100%)\"),\n                                                        WebkitAppearance: 'none',\n                                                        outline: 'none'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 849,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 863,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 864,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 862,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 848,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setUserCount(Math.max(1, userCount - 1)),\n                                                    className: \"w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 flex items-center justify-center text-gray-700 dark:text-gray-300\",\n                                                    children: \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 870,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            min: \"1\",\n                                                            max: \"200\",\n                                                            value: userCount,\n                                                            onChange: (e)=>setUserCount(Math.max(1, Math.min(200, parseInt(e.target.value) || 1))),\n                                                            className: \"w-20 text-center text-2xl font-bold text-orange-600 dark:text-orange-400 bg-transparent border-none focus:outline-none\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 878,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                            children: \"usu\\xe1rios\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 886,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setUserCount(Math.min(200, userCount + 1)),\n                                                    className: \"w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 flex items-center justify-center text-gray-700 dark:text-gray-300\",\n                                                    children: \"+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 888,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 869,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 justify-center\",\n                                            children: [\n                                                5,\n                                                10,\n                                                20,\n                                                50,\n                                                100,\n                                                200\n                                            ].map((count)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setUserCount(count),\n                                                    className: \"px-3 py-1 rounded-full text-xs font-medium transition-colors \".concat(userCount === count ? 'bg-orange-500 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500'),\n                                                    children: count\n                                                }, count, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 900,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 898,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 846,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 836,\n                            columnNumber: 13\n                        }, this),\n                        (()=>{\n                            const pricing = calculatePrice(userCount);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 border-2 border-orange-200 dark:border-orange-700 rounded-xl p-6 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\",\n                                            children: \"Resumo do seu plano\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 923,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: \"Usu\\xe1rios:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 931,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                                    children: userCount\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 932,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 930,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: \"Pre\\xe7o por usu\\xe1rio:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 935,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        basePrice.toFixed(2).replace('.', ',')\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 936,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 934,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        pricing.discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: \"Desconto:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 940,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-green-600 dark:text-green-400\",\n                                                                    children: [\n                                                                        pricing.discount,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 941,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 939,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: \"Ciclo:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 945,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                                    children: billingCycle === 'monthly' ? 'Mensal' : 'Anual'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 946,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 944,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 929,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        (pricing.discount > 0 || billingCycle === 'yearly') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500 dark:text-gray-400 line-through\",\n                                                                    children: \"Valor sem desconto:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 956,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500 dark:text-gray-400 line-through\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        (billingCycle === 'monthly' ? pricing.totalWithoutDiscount : pricing.yearlyPriceWithoutAnnualDiscount).toFixed(2).replace('.', ',')\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 959,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 955,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                                    children: [\n                                                                        \"Valor \",\n                                                                        billingCycle === 'monthly' ? 'mensal' : 'anual',\n                                                                        \":\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 965,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-orange-600 dark:text-orange-400\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        (billingCycle === 'monthly' ? pricing.monthlyPrice : pricing.yearlyPrice).toFixed(2).replace('.', ',')\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                    lineNumber: 968,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 964,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        pricing.discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 px-3 py-1 rounded-full text-sm font-medium\",\n                                                                children: [\n                                                                    \"Economia de R$ \",\n                                                                    pricing.discountAmount.toFixed(2).replace('.', ','),\n                                                                    \" por m\\xeas (\",\n                                                                    pricing.discount,\n                                                                    \"% desconto)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                lineNumber: 976,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 975,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        billingCycle === 'yearly' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 px-3 py-1 rounded-full text-sm font-medium\",\n                                                                children: [\n                                                                    \"Economia anual de R$ \",\n                                                                    pricing.annualSavings.toFixed(2).replace('.', ','),\n                                                                    \" (2 meses gr\\xe1tis)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                                lineNumber: 985,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 984,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 953,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 927,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white dark:bg-gray-800 rounded-lg p-4 border border-orange-200 dark:border-orange-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white mb-3\",\n                                                    children: \"Faixas de desconto:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 995,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 md:grid-cols-5 gap-2 text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded \".concat(userCount >= 5 && userCount < 20 ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300' : 'text-gray-600 dark:text-gray-400'),\n                                                            children: \"5-19 usu\\xe1rios: 10%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 997,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded \".concat(userCount >= 20 && userCount < 50 ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300' : 'text-gray-600 dark:text-gray-400'),\n                                                            children: \"20-49 usu\\xe1rios: 15%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 1000,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded \".concat(userCount >= 50 && userCount < 100 ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300' : 'text-gray-600 dark:text-gray-400'),\n                                                            children: \"50-99 usu\\xe1rios: 25%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 1003,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded \".concat(userCount >= 100 && userCount < 200 ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300' : 'text-gray-600 dark:text-gray-400'),\n                                                            children: \"100-199 usu\\xe1rios: 35%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 1006,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded \".concat(userCount >= 200 ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300' : 'text-gray-600 dark:text-gray-400'),\n                                                            children: \"200+ usu\\xe1rios: 40%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                            lineNumber: 1009,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 996,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 994,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 922,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                lineNumber: 921,\n                                columnNumber: 17\n                            }, this);\n                        })(),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 dark:bg-gray-700 rounded-xl p-6 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                    className: \"font-semibold text-gray-900 dark:text-white mb-3\",\n                                    children: \"Todos os planos incluem:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 1021,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-500 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 1024,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                    children: \"Sistema de Agendamento\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 1025,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 1023,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-500 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 1028,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                    children: \"Gest\\xe3o de Pacientes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 1029,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 1027,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-500 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 1032,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                    children: \"Calend\\xe1rio Integrado\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 1033,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 1031,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-500 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 1036,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                    children: \"Notifica\\xe7\\xf5es Autom\\xe1ticas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                    lineNumber: 1037,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 1035,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 1022,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-3 bg-orange-100 dark:bg-orange-900/30 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-orange-800 dark:text-orange-300 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"M\\xf3dulos adicionais\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 1042,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" como Financeiro, RH e ABA+ podem ser contratados separadamente ap\\xf3s a assinatura.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                        lineNumber: 1041,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 1040,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 1020,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between pt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handlePrevStep,\n                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                            lineNumber: 1053,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Anterior\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 1048,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleSubmit,\n                                    disabled: isLoading,\n                                    className: \"inline-flex items-center px-8 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-orange-500 hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 1065,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Processando...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            \"Finalizar e Pagar\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Calendar_CheckCircle_CreditCard_Lock_Mail_MapPin_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-5 h-5 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                                lineNumber: 1071,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                                    lineNumber: 1057,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                            lineNumber: 1047,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n                    lineNumber: 785,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n            lineNumber: 274,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\subscription\\\\signup\\\\page.js\",\n        lineNumber: 273,\n        columnNumber: 5\n    }, this);\n}\n_s(SubscriptionSignupPage, \"nplT6jpYkViVJh52yotzhTl1Kfg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SubscriptionSignupPage;\nvar _c;\n$RefreshReg$(_c, \"SubscriptionSignupPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/subscription/signup/page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/pricing.js":
/*!******************************!*\
  !*** ./src/utils/pricing.js ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PRICING_CONFIG: () => (/* binding */ PRICING_CONFIG),\n/* harmony export */   USER_OPTIONS: () => (/* binding */ USER_OPTIONS),\n/* harmony export */   calculateAdditionalUsersCost: () => (/* binding */ calculateAdditionalUsersCost),\n/* harmony export */   calculatePrice: () => (/* binding */ calculatePrice),\n/* harmony export */   calculateSavings: () => (/* binding */ calculateSavings),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   getDiscountByUserCount: () => (/* binding */ getDiscountByUserCount),\n/* harmony export */   getNextDiscountTier: () => (/* binding */ getNextDiscountTier),\n/* harmony export */   isValidUserCount: () => (/* binding */ isValidUserCount)\n/* harmony export */ });\n// Configuração de preços centralizada (deve ser igual ao backend)\nconst PRICING_CONFIG = {\n    basePrice: 19.90,\n    annualDiscount: 0.10,\n    // Descontos por quantidade de usuários\n    userDiscounts: [\n        {\n            minUsers: 200,\n            discount: 40\n        },\n        {\n            minUsers: 100,\n            discount: 35\n        },\n        {\n            minUsers: 50,\n            discount: 25\n        },\n        {\n            minUsers: 20,\n            discount: 15\n        },\n        {\n            minUsers: 5,\n            discount: 10\n        },\n        {\n            minUsers: 1,\n            discount: 0\n        }\n    ]\n};\n/**\n * Calcula desconto baseado na quantidade de usuários\n * @param {number} users - Quantidade de usuários\n * @returns {number} - Percentual de desconto (0-40)\n */ const getDiscountByUserCount = (users)=>{\n    for (const tier of PRICING_CONFIG.userDiscounts){\n        if (users >= tier.minUsers) {\n            return tier.discount;\n        }\n    }\n    return 0;\n};\n/**\n * Calcula preços completos baseado na quantidade de usuários\n * @param {number} users - Quantidade de usuários\n * @param {boolean} isAnnual - Se é pagamento anual\n * @returns {Object} - Objeto com todos os cálculos de preço\n */ const calculatePrice = function(users) {\n    let isAnnual = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n    const discount = getDiscountByUserCount(users);\n    const totalWithoutDiscount = users * PRICING_CONFIG.basePrice;\n    const discountAmount = totalWithoutDiscount * (discount / 100);\n    const monthlyPrice = totalWithoutDiscount - discountAmount;\n    // Preço anual com desconto adicional\n    const yearlyPriceWithoutAnnualDiscount = monthlyPrice * 12;\n    const yearlyPrice = yearlyPriceWithoutAnnualDiscount * (1 - PRICING_CONFIG.annualDiscount);\n    return {\n        totalWithoutDiscount,\n        discountAmount,\n        discount,\n        monthlyPrice,\n        yearlyPrice,\n        yearlyPriceWithoutAnnualDiscount,\n        annualDiscount: PRICING_CONFIG.annualDiscount * 100,\n        pricePerUser: monthlyPrice / users,\n        finalPrice: isAnnual ? yearlyPrice : monthlyPrice,\n        annualSavings: yearlyPriceWithoutAnnualDiscount - yearlyPrice\n    };\n};\n/**\n * Formata valor monetário para exibição\n * @param {number} value - Valor a ser formatado\n * @returns {string} - Valor formatado em R$\n */ const formatCurrency = (value)=>{\n    return new Intl.NumberFormat('pt-BR', {\n        style: 'currency',\n        currency: 'BRL'\n    }).format(value);\n};\n/**\n * Calcula economia total (desconto por quantidade + desconto anual)\n * @param {number} users - Quantidade de usuários\n * @param {boolean} isAnnual - Se é pagamento anual\n * @returns {Object} - Objeto com informações de economia\n */ const calculateSavings = function(users) {\n    let isAnnual = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n    const pricing = calculatePrice(users, isAnnual);\n    const fullPriceMonthly = users * PRICING_CONFIG.basePrice;\n    const fullPriceYearly = fullPriceMonthly * 12;\n    let totalSavings = 0;\n    let savingsBreakdown = [];\n    // Economia por quantidade\n    if (pricing.discount > 0) {\n        const quantitySavings = isAnnual ? pricing.discountAmount * 12 : pricing.discountAmount;\n        totalSavings += quantitySavings;\n        savingsBreakdown.push({\n            type: 'quantity',\n            label: \"Desconto por \".concat(users, \" usu\\xe1rios (\").concat(pricing.discount, \"%)\"),\n            amount: quantitySavings,\n            monthly: pricing.discountAmount\n        });\n    }\n    // Economia anual\n    if (isAnnual) {\n        const annualSavings = pricing.annualSavings;\n        totalSavings += annualSavings;\n        savingsBreakdown.push({\n            type: 'annual',\n            label: \"Desconto anual (\".concat(pricing.annualDiscount, \"%)\"),\n            amount: annualSavings,\n            monthly: annualSavings / 12\n        });\n    }\n    return {\n        totalSavings,\n        savingsBreakdown,\n        fullPrice: isAnnual ? fullPriceYearly : fullPriceMonthly,\n        finalPrice: pricing.finalPrice,\n        savingsPercentage: totalSavings > 0 ? totalSavings / (isAnnual ? fullPriceYearly : fullPriceMonthly) * 100 : 0\n    };\n};\n/**\n * Opções de usuários predefinidas para seleção\n */ const USER_OPTIONS = [\n    1,\n    5,\n    20,\n    50,\n    100,\n    200\n];\n/**\n * Valida se a quantidade de usuários está dentro dos limites\n * @param {number} users - Quantidade de usuários\n * @returns {boolean} - Se é válido\n */ const isValidUserCount = (users)=>{\n    return users >= 1 && users <= 1000 && Number.isInteger(users);\n};\n/**\n * Obtém a próxima faixa de desconto\n * @param {number} currentUsers - Quantidade atual de usuários\n * @returns {Object|null} - Próxima faixa ou null se já está na máxima\n */ const getNextDiscountTier = (currentUsers)=>{\n    for (const tier of PRICING_CONFIG.userDiscounts){\n        if (currentUsers < tier.minUsers) {\n            return {\n                minUsers: tier.minUsers,\n                discount: tier.discount,\n                usersToNext: tier.minUsers - currentUsers\n            };\n        }\n    }\n    return null;\n};\n/**\n * Calcula o custo adicional para adicionar mais usuários\n * @param {number} currentUsers - Quantidade atual de usuários\n * @param {number} additionalUsers - Usuários a adicionar\n * @param {boolean} isAnnual - Se é pagamento anual\n * @returns {Object} - Informações sobre o custo adicional\n */ const calculateAdditionalUsersCost = function(currentUsers, additionalUsers) {\n    let isAnnual = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n    const currentPricing = calculatePrice(currentUsers, isAnnual);\n    const newPricing = calculatePrice(currentUsers + additionalUsers, isAnnual);\n    const additionalCost = newPricing.finalPrice - currentPricing.finalPrice;\n    const costPerAdditionalUser = additionalCost / additionalUsers;\n    return {\n        currentPrice: currentPricing.finalPrice,\n        newPrice: newPricing.finalPrice,\n        additionalCost,\n        costPerAdditionalUser,\n        newDiscount: newPricing.discount,\n        discountImprovement: newPricing.discount - currentPricing.discount\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/pricing.js\n"));

/***/ })

});